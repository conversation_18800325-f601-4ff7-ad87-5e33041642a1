﻿namespace ProFaceSimple;

partial class MainForm
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        groupBoxConnection = new GroupBox();
        buttonConnect = new Button();
        textBoxPort = new TextBox();
        textBoxIP = new TextBox();
        labelPort = new Label();
        labelIP = new Label();
        groupBoxData = new GroupBox();
        buttonReadData = new Button();
        textBoxAddress = new TextBox();
        labelAddress = new Label();
        dataGridViewResults = new DataGridView();
        groupBoxDatabase = new GroupBox();
        buttonSaveToDatabase = new Button();
        textBoxConnectionString = new TextBox();
        labelConnectionString = new Label();
        statusStrip = new StatusStrip();
        toolStripStatusLabel = new ToolStripStatusLabel();
        groupBoxConnection.SuspendLayout();
        groupBoxData.SuspendLayout();
        ((System.ComponentModel.ISupportInitialize)dataGridViewResults).BeginInit();
        groupBoxDatabase.SuspendLayout();
        statusStrip.SuspendLayout();
        SuspendLayout();
        //
        // groupBoxConnection
        //
        groupBoxConnection.Controls.Add(buttonConnect);
        groupBoxConnection.Controls.Add(textBoxPort);
        groupBoxConnection.Controls.Add(textBoxIP);
        groupBoxConnection.Controls.Add(labelPort);
        groupBoxConnection.Controls.Add(labelIP);
        groupBoxConnection.Location = new Point(12, 12);
        groupBoxConnection.Name = "groupBoxConnection";
        groupBoxConnection.Size = new Size(300, 120);
        groupBoxConnection.TabIndex = 0;
        groupBoxConnection.TabStop = false;
        groupBoxConnection.Text = "Conexión Modbus";
        //
        // buttonConnect
        //
        buttonConnect.Location = new Point(200, 80);
        buttonConnect.Name = "buttonConnect";
        buttonConnect.Size = new Size(80, 30);
        buttonConnect.TabIndex = 4;
        buttonConnect.Text = "Conectar";
        buttonConnect.UseVisualStyleBackColor = true;
        buttonConnect.Click += ButtonConnect_Click;
        //
        // textBoxPort
        //
        textBoxPort.Location = new Point(80, 50);
        textBoxPort.Name = "textBoxPort";
        textBoxPort.Size = new Size(100, 23);
        textBoxPort.TabIndex = 3;
        textBoxPort.Text = "502";
        //
        // textBoxIP
        //
        textBoxIP.Location = new Point(80, 20);
        textBoxIP.Name = "textBoxIP";
        textBoxIP.Size = new Size(200, 23);
        textBoxIP.TabIndex = 1;
        textBoxIP.Text = "*************";
        //
        // labelPort
        //
        labelPort.AutoSize = true;
        labelPort.Location = new Point(20, 53);
        labelPort.Name = "labelPort";
        labelPort.Size = new Size(42, 15);
        labelPort.TabIndex = 2;
        labelPort.Text = "Puerto:";
        //
        // labelIP
        //
        labelIP.AutoSize = true;
        labelIP.Location = new Point(20, 23);
        labelIP.Name = "labelIP";
        labelIP.Size = new Size(20, 15);
        labelIP.TabIndex = 0;
        labelIP.Text = "IP:";
        //
        // groupBoxData
        //
        groupBoxData.Controls.Add(buttonReadData);
        groupBoxData.Controls.Add(textBoxAddress);
        groupBoxData.Controls.Add(labelAddress);
        groupBoxData.Location = new Point(330, 12);
        groupBoxData.Name = "groupBoxData";
        groupBoxData.Size = new Size(300, 120);
        groupBoxData.TabIndex = 1;
        groupBoxData.TabStop = false;
        groupBoxData.Text = "Lectura de Datos";
        //
        // buttonReadData
        //
        buttonReadData.Location = new Point(200, 80);
        buttonReadData.Name = "buttonReadData";
        buttonReadData.Size = new Size(80, 30);
        buttonReadData.TabIndex = 2;
        buttonReadData.Text = "Leer";
        buttonReadData.UseVisualStyleBackColor = true;
        buttonReadData.Click += ButtonReadData_Click;
        //
        // textBoxAddress
        //
        textBoxAddress.Location = new Point(80, 20);
        textBoxAddress.Name = "textBoxAddress";
        textBoxAddress.Size = new Size(200, 23);
        textBoxAddress.TabIndex = 1;
        textBoxAddress.Text = "40001";
        //
        // labelAddress
        //
        labelAddress.AutoSize = true;
        labelAddress.Location = new Point(20, 23);
        labelAddress.Name = "labelAddress";
        labelAddress.Size = new Size(60, 15);
        labelAddress.TabIndex = 0;
        labelAddress.Text = "Dirección:";
        //
        // dataGridViewResults
        //
        dataGridViewResults.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
        dataGridViewResults.Location = new Point(12, 150);
        dataGridViewResults.Name = "dataGridViewResults";
        dataGridViewResults.Size = new Size(618, 200);
        dataGridViewResults.TabIndex = 2;
        //
        // groupBoxDatabase
        //
        groupBoxDatabase.Controls.Add(buttonSaveToDatabase);
        groupBoxDatabase.Controls.Add(textBoxConnectionString);
        groupBoxDatabase.Controls.Add(labelConnectionString);
        groupBoxDatabase.Location = new Point(12, 370);
        groupBoxDatabase.Name = "groupBoxDatabase";
        groupBoxDatabase.Size = new Size(618, 80);
        groupBoxDatabase.TabIndex = 3;
        groupBoxDatabase.TabStop = false;
        groupBoxDatabase.Text = "Base de Datos";
        //
        // buttonSaveToDatabase
        //
        buttonSaveToDatabase.Location = new Point(520, 40);
        buttonSaveToDatabase.Name = "buttonSaveToDatabase";
        buttonSaveToDatabase.Size = new Size(80, 30);
        buttonSaveToDatabase.TabIndex = 2;
        buttonSaveToDatabase.Text = "Guardar";
        buttonSaveToDatabase.UseVisualStyleBackColor = true;
        buttonSaveToDatabase.Click += ButtonSaveToDatabase_Click;
        //
        // textBoxConnectionString
        //
        textBoxConnectionString.Location = new Point(120, 20);
        textBoxConnectionString.Name = "textBoxConnectionString";
        textBoxConnectionString.Size = new Size(480, 23);
        textBoxConnectionString.TabIndex = 1;
        textBoxConnectionString.Text = "Server=localhost;Database=ProFaceData;Integrated Security=true;";
        //
        // labelConnectionString
        //
        labelConnectionString.AutoSize = true;
        labelConnectionString.Location = new Point(20, 23);
        labelConnectionString.Name = "labelConnectionString";
        labelConnectionString.Size = new Size(94, 15);
        labelConnectionString.TabIndex = 0;
        labelConnectionString.Text = "Cadena Conexión:";
        //
        // statusStrip
        //
        statusStrip.Items.AddRange(new ToolStripItem[] { toolStripStatusLabel });
        statusStrip.Location = new Point(0, 468);
        statusStrip.Name = "statusStrip";
        statusStrip.Size = new Size(650, 22);
        statusStrip.TabIndex = 4;
        statusStrip.Text = "statusStrip1";
        //
        // toolStripStatusLabel
        //
        toolStripStatusLabel.Name = "toolStripStatusLabel";
        toolStripStatusLabel.Size = new Size(42, 17);
        toolStripStatusLabel.Text = "Listo...";
        //
        // MainForm
        //
        AutoScaleDimensions = new SizeF(7F, 15F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(650, 490);
        Controls.Add(statusStrip);
        Controls.Add(groupBoxDatabase);
        Controls.Add(dataGridViewResults);
        Controls.Add(groupBoxData);
        Controls.Add(groupBoxConnection);
        Name = "MainForm";
        Text = "ProFace Data Extractor - Simple";
        groupBoxConnection.ResumeLayout(false);
        groupBoxConnection.PerformLayout();
        groupBoxData.ResumeLayout(false);
        groupBoxData.PerformLayout();
        ((System.ComponentModel.ISupportInitialize)dataGridViewResults).EndInit();
        groupBoxDatabase.ResumeLayout(false);
        groupBoxDatabase.PerformLayout();
        statusStrip.ResumeLayout(false);
        statusStrip.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }

    #endregion

    private GroupBox groupBoxConnection;
    private Button buttonConnect;
    private TextBox textBoxPort;
    private TextBox textBoxIP;
    private Label labelPort;
    private Label labelIP;
    private GroupBox groupBoxData;
    private Button buttonReadData;
    private TextBox textBoxAddress;
    private Label labelAddress;
    private DataGridView dataGridViewResults;
    private GroupBox groupBoxDatabase;
    private Button buttonSaveToDatabase;
    private TextBox textBoxConnectionString;
    private Label labelConnectionString;
    private StatusStrip statusStrip;
    private ToolStripStatusLabel toolStripStatusLabel;
}
