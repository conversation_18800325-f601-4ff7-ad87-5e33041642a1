using System;
using System.Collections.Generic;

namespace ProFaceDataExtractor.Models
{
    public class GatewayConfiguration
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string IpAddress { get; set; } = "*************";
        public int Port { get; set; } = 502;
        public int Timeout { get; set; } = 5000;
        public int RetryCount { get; set; } = 3;
        public bool IsEnabled { get; set; } = true;
        public List<PLCConfiguration> PLCs { get; set; } = new List<PLCConfiguration>();
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string Description { get; set; } = string.Empty;
    }
}