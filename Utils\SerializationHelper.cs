using System;
using System.IO;
using System.Text.Json;
using Newtonsoft.Json;

namespace ProFaceDataExtractor.Utils
{
    public static class SerializationHelper
    {
        public static string SerializeToJson<T>(T obj)
        {
            try
            {
                return JsonConvert.SerializeObject(obj, Formatting.Indented);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error serializando objeto: {ex.Message}", ex);
            }
        }

        public static T? DeserializeFromJson<T>(string json)
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error deserializando JSON: {ex.Message}", ex);
            }
        }

        public static void SaveToFile<T>(T obj, string filePath)
        {
            try
            {
                var json = SerializeToJson(obj);
                File.WriteAllText(filePath, json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error guardando archivo: {ex.Message}", ex);
            }
        }

        public static T? LoadFromFile<T>(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return default(T);

                var json = File.ReadAllText(filePath);
                return DeserializeFromJson<T>(json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error cargando archivo: {ex.Message}", ex);
            }
        }
    }
}