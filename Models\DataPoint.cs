using System;

namespace ProFaceDataExtractor.Models
{
    public enum DataType
    {
        Int16,
        Int32,
        Float,
        Bool,
        String
    }

    public class DataPoint
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;
        public DataType DataType { get; set; } = DataType.Int16;
        public bool IsEnabled { get; set; } = true;
        public double ScaleFactor { get; set; } = 1.0;
        public double Offset { get; set; } = 0.0;
        public string Unit { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public object? LastValue { get; set; }
        public DateTime LastReadTime { get; set; } = DateTime.MinValue;
    }
}