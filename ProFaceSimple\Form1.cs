using EasyModbus;
using Microsoft.Data.SqlClient;
using System.Data;

namespace ProFaceSimple;

public partial class MainForm : Form
{
    private ModbusClient? modbusClient;
    private DataTable dataTable;
    private bool isConnected = false;

    public MainForm()
    {
        InitializeComponent();
        InitializeDataTable();
    }

    private void InitializeDataTable()
    {
        dataTable = new DataTable();
        dataTable.Columns.Add("Timestamp", typeof(DateTime));
        dataTable.Columns.Add("Address", typeof(string));
        dataTable.Columns.Add("Value", typeof(int));
        dataTable.Columns.Add("Description", typeof(string));

        dataGridViewResults.DataSource = dataTable;
        dataGridViewResults.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
    }

    private void ButtonConnect_Click(object sender, EventArgs e)
    {
        try
        {
            if (!isConnected)
            {
                // Crear nueva conexión
                modbusClient = new ModbusClient(textBoxIP.Text, int.Parse(textBoxPort.Text));
                modbusClient.Connect();

                isConnected = true;
                buttonConnect.Text = "Desconectar";
                buttonConnect.BackColor = Color.LightGreen;
                toolStripStatusLabel.Text = $"Conectado a {textBoxIP.Text}:{textBoxPort.Text}";

                // Habilitar controles
                buttonReadData.Enabled = true;
            }
            else
            {
                // Desconectar
                modbusClient?.Disconnect();
                modbusClient = null;

                isConnected = false;
                buttonConnect.Text = "Conectar";
                buttonConnect.BackColor = SystemColors.Control;
                toolStripStatusLabel.Text = "Desconectado";

                // Deshabilitar controles
                buttonReadData.Enabled = false;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error de conexión: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            toolStripStatusLabel.Text = $"Error: {ex.Message}";
        }
    }

    private void ButtonReadData_Click(object sender, EventArgs e)
    {
        if (!isConnected || modbusClient == null)
        {
            MessageBox.Show("Debe conectarse primero", "Advertencia",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            // Parsear dirección Modbus
            string address = textBoxAddress.Text.Trim();
            int modbusAddress;

            if (address.StartsWith("4"))
            {
                // Holding Register (40001 -> 0)
                modbusAddress = int.Parse(address) - 40001;
            }
            else if (address.StartsWith("3"))
            {
                // Input Register (30001 -> 0)
                modbusAddress = int.Parse(address) - 30001;
            }
            else
            {
                modbusAddress = int.Parse(address);
            }

            // Leer datos
            int[] values = modbusClient.ReadHoldingRegisters(modbusAddress, 1);

            // Agregar a la tabla
            DataRow row = dataTable.NewRow();
            row["Timestamp"] = DateTime.Now;
            row["Address"] = address;
            row["Value"] = values[0];
            row["Description"] = $"Registro {address}";
            dataTable.Rows.Add(row);

            // Scroll al final
            if (dataGridViewResults.Rows.Count > 0)
            {
                dataGridViewResults.FirstDisplayedScrollingRowIndex = dataGridViewResults.Rows.Count - 1;
            }

            toolStripStatusLabel.Text = $"Leído: {address} = {values[0]}";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error al leer datos: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            toolStripStatusLabel.Text = $"Error lectura: {ex.Message}";
        }
    }

    private void ButtonSaveToDatabase_Click(object sender, EventArgs e)
    {
        if (dataTable.Rows.Count == 0)
        {
            MessageBox.Show("No hay datos para guardar", "Advertencia",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
            return;
        }

        try
        {
            using var connection = new SqlConnection(textBoxConnectionString.Text);
            connection.Open();

            // Crear tabla si no existe
            string createTableSql = @"
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ModbusData' AND xtype='U')
                CREATE TABLE ModbusData (
                    Id int IDENTITY(1,1) PRIMARY KEY,
                    Timestamp datetime NOT NULL,
                    Address nvarchar(50) NOT NULL,
                    Value int NOT NULL,
                    Description nvarchar(200)
                )";

            using (var createCommand = new SqlCommand(createTableSql, connection))
            {
                createCommand.ExecuteNonQuery();
            }

            // Insertar datos
            int savedCount = 0;
            foreach (DataRow row in dataTable.Rows)
            {
                string insertSql = @"
                    INSERT INTO ModbusData (Timestamp, Address, Value, Description)
                    VALUES (@Timestamp, @Address, @Value, @Description)";

                using var insertCommand = new SqlCommand(insertSql, connection);
                insertCommand.Parameters.AddWithValue("@Timestamp", row["Timestamp"]);
                insertCommand.Parameters.AddWithValue("@Address", row["Address"]);
                insertCommand.Parameters.AddWithValue("@Value", row["Value"]);
                insertCommand.Parameters.AddWithValue("@Description", row["Description"]);

                insertCommand.ExecuteNonQuery();
                savedCount++;
            }

            MessageBox.Show($"Se guardaron {savedCount} registros en la base de datos",
                "Éxito", MessageBoxButtons.OK, MessageBoxIcon.Information);
            toolStripStatusLabel.Text = $"Guardados {savedCount} registros en BD";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error al guardar en base de datos: {ex.Message}", "Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            toolStripStatusLabel.Text = $"Error BD: {ex.Message}";
        }
    }

    protected override void OnFormClosing(FormClosingEventArgs e)
    {
        // Desconectar al cerrar
        if (isConnected && modbusClient != null)
        {
            try
            {
                modbusClient.Disconnect();
            }
            catch { }
        }
        base.OnFormClosing(e);
    }
}
