namespace ProFaceDataExtractor.Forms
{
    partial class MainForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.menuStrip = new System.Windows.Forms.MenuStrip();
            this.archivoToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.exportarConfiguracionToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.importarConfiguracionToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.salirToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.configuracionToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.configuracionGeneralToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ayudaToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.acercaDeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStrip = new System.Windows.Forms.ToolStrip();
            this.btnToolStart = new System.Windows.Forms.ToolStripButton();
            this.btnToolStop = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.btnToolExport = new System.Windows.Forms.ToolStripButton();
            this.statusStrip = new System.Windows.Forms.StatusStrip();
            this.statusLabel = new System.Windows.Forms.ToolStripStatusLabel();
            this.updateLabel = new System.Windows.Forms.ToolStripStatusLabel();
            this.progressLabel = new System.Windows.Forms.ToolStripProgressBar();
            this.splitContainer = new System.Windows.Forms.SplitContainer();
            this.leftPanel = new System.Windows.Forms.Panel();
            this.treeViewGateways = new System.Windows.Forms.TreeView();
            this.groupBoxControls = new System.Windows.Forms.GroupBox();
            this.btnExportNow = new System.Windows.Forms.Button();
            this.btnStop = new System.Windows.Forms.Button();
            this.btnStart = new System.Windows.Forms.Button();
            this.btnDeleteGateway = new System.Windows.Forms.Button();
            this.btnEditGateway = new System.Windows.Forms.Button();
            this.btnAddGateway = new System.Windows.Forms.Button();
            this.dataGridViewData = new System.Windows.Forms.DataGridView();
            this.menuStrip.SuspendLayout();
            this.toolStrip.SuspendLayout();
            this.statusStrip.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).BeginInit();
            this.splitContainer.Panel1.SuspendLayout();
            this.splitContainer.Panel2.SuspendLayout();
            this.splitContainer.SuspendLayout();
            this.leftPanel.SuspendLayout();
            this.groupBoxControls.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewData)).BeginInit();
            this.SuspendLayout();
            // 
            // menuStrip
            // 
            this.menuStrip.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.menuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.archivoToolStripMenuItem,
            this.configuracionToolStripMenuItem,
            this.ayudaToolStripMenuItem});
            this.menuStrip.Location = new System.Drawing.Point(0, 0);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Size = new System.Drawing.Size(1200, 28);
            this.menuStrip.TabIndex = 0;
            this.menuStrip.Text = "menuStrip1";
            // 
            // archivoToolStripMenuItem
            // 
            this.archivoToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.exportarConfiguracionToolStripMenuItem,
            this.importarConfiguracionToolStripMenuItem,
            this.toolStripSeparator1,
            this.salirToolStripMenuItem});
            this.archivoToolStripMenuItem.Name = "archivoToolStripMenuItem";
            this.archivoToolStripMenuItem.Size = new System.Drawing.Size(73, 24);
            this.archivoToolStripMenuItem.Text = "Archivo";
            // 
            // exportarConfiguracionToolStripMenuItem
            // 
            this.exportarConfiguracionToolStripMenuItem.Name = "exportarConfiguracionToolStripMenuItem";
            this.exportarConfiguracionToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.exportarConfiguracionToolStripMenuItem.Text = "Exportar Configuración";
            this.exportarConfiguracionToolStripMenuItem.Click += new System.EventHandler(this.ExportConfig_Click);
            // 
            // importarConfiguracionToolStripMenuItem
            // 
            this.importarConfiguracionToolStripMenuItem.Name = "importarConfiguracionToolStripMenuItem";
            this.importarConfiguracionToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.importarConfiguracionToolStripMenuItem.Text = "Importar Configuración";
            this.importarConfiguracionToolStripMenuItem.Click += new System.EventHandler(this.ImportConfig_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(243, 6);
            // 
            // salirToolStripMenuItem
            // 
            this.salirToolStripMenuItem.Name = "salirToolStripMenuItem";
            this.salirToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.salirToolStripMenuItem.Text = "Salir";
            this.salirToolStripMenuItem.Click += new System.EventHandler(this.SalirToolStripMenuItem_Click);
            // 
            // configuracionToolStripMenuItem
            // 
            this.configuracionToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.configuracionGeneralToolStripMenuItem});
            this.configuracionToolStripMenuItem.Name = "configuracionToolStripMenuItem";
            this.configuracionToolStripMenuItem.Size = new System.Drawing.Size(116, 24);
            this.configuracionToolStripMenuItem.Text = "Configuración";
            // 
            // configuracionGeneralToolStripMenuItem
            // 
            this.configuracionGeneralToolStripMenuItem.Name = "configuracionGeneralToolStripMenuItem";
            this.configuracionGeneralToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.configuracionGeneralToolStripMenuItem.Text = "Configuración General";
            this.configuracionGeneralToolStripMenuItem.Click += new System.EventHandler(this.Settings_Click);
            // 
            // ayudaToolStripMenuItem
            // 
            this.ayudaToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.acercaDeToolStripMenuItem});
            this.ayudaToolStripMenuItem.Name = "ayudaToolStripMenuItem";
            this.ayudaToolStripMenuItem.Size = new System.Drawing.Size(65, 24);
            this.ayudaToolStripMenuItem.Text = "Ayuda";
            // 
            // acercaDeToolStripMenuItem
            // 
            this.acercaDeToolStripMenuItem.Name = "acercaDeToolStripMenuItem";
            this.acercaDeToolStripMenuItem.Size = new System.Drawing.Size(224, 26);
            this.acercaDeToolStripMenuItem.Text = "Acerca de";
            this.acercaDeToolStripMenuItem.Click += new System.EventHandler(this.About_Click);
            // 
            // toolStrip
            // 
            this.toolStrip.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.toolStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnToolStart,
            this.btnToolStop,
            this.toolStripSeparator2,
            this.btnToolExport});
            this.toolStrip.Location = new System.Drawing.Point(0, 28);
            this.toolStrip.Name = "toolStrip";
            this.toolStrip.Size = new System.Drawing.Size(1200, 27);
            this.toolStrip.TabIndex = 1;
            this.toolStrip.Text = "toolStrip1";
            // 
            // btnToolStart
            //
            this.btnToolStart.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnToolStart.Name = "btnToolStart";
            this.btnToolStart.Size = new System.Drawing.Size(70, 24);
            this.btnToolStart.Text = "Iniciar";
            this.btnToolStart.Click += new System.EventHandler(this.BtnStart_Click);
            //
            // btnToolStop
            //
            this.btnToolStop.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnToolStop.Name = "btnToolStop";
            this.btnToolStop.Size = new System.Drawing.Size(78, 24);
            this.btnToolStop.Text = "Detener";
            this.btnToolStop.Click += new System.EventHandler(this.BtnStop_Click);
            //
            // toolStripSeparator2
            //
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(6, 27);
            //
            // btnToolExport
            //
            this.btnToolExport.ImageTransparentColor = System.Drawing.Color.Magenta;
            this.btnToolExport.Name = "btnToolExport";
            this.btnToolExport.Size = new System.Drawing.Size(108, 24);
            this.btnToolExport.Text = "Exportar CSV";
            this.btnToolExport.Click += new System.EventHandler(this.BtnExportNow_Click);
            // 
            // statusStrip
            // 
            this.statusStrip.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.statusStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.statusLabel,
            this.updateLabel,
            this.progressLabel});
            this.statusStrip.Location = new System.Drawing.Point(0, 745);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(1200, 26);
            this.statusStrip.TabIndex = 2;
            this.statusStrip.Text = "statusStrip1";
            // 
            // statusLabel
            // 
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Size = new System.Drawing.Size(69, 20);
            this.statusLabel.Text = "Detenido";
            // 
            // updateLabel
            // 
            this.updateLabel.Name = "updateLabel";
            this.updateLabel.Size = new System.Drawing.Size(201, 20);
            this.updateLabel.Text = "Última actualización: Nunca";
            // 
            // progressLabel
            // 
            this.progressLabel.Name = "progressLabel";
            this.progressLabel.Size = new System.Drawing.Size(200, 20);
            // 
            // splitContainer
            // 
            this.splitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer.Location = new System.Drawing.Point(0, 55);
            this.splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            this.splitContainer.Panel1.Controls.Add(this.leftPanel);
            // 
            // splitContainer.Panel2
            // 
            this.splitContainer.Panel2.Controls.Add(this.dataGridViewData);
            this.splitContainer.Size = new System.Drawing.Size(1200, 690);
            this.splitContainer.SplitterDistance = 300;
            this.splitContainer.TabIndex = 3;
            // 
            // leftPanel
            // 
            this.leftPanel.Controls.Add(this.treeViewGateways);
            this.leftPanel.Controls.Add(this.groupBoxControls);
            this.leftPanel.Dock = System.Windows.Forms.DockStyle.Fill;
            this.leftPanel.Location = new System.Drawing.Point(0, 0);
            this.leftPanel.Name = "leftPanel";
            this.leftPanel.Size = new System.Drawing.Size(300, 690);
            this.leftPanel.TabIndex = 0;
            // 
            // treeViewGateways
            // 
            this.treeViewGateways.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewGateways.Location = new System.Drawing.Point(0, 80);
            this.treeViewGateways.Name = "treeViewGateways";
            this.treeViewGateways.ShowNodeToolTips = true;
            this.treeViewGateways.Size = new System.Drawing.Size(300, 610);
            this.treeViewGateways.TabIndex = 1;
            this.treeViewGateways.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.TreeViewGateways_AfterSelect);
            // 
            // groupBoxControls
            // 
            this.groupBoxControls.Controls.Add(this.btnExportNow);
            this.groupBoxControls.Controls.Add(this.btnStop);
            this.groupBoxControls.Controls.Add(this.btnStart);
            this.groupBoxControls.Controls.Add(this.btnDeleteGateway);
            this.groupBoxControls.Controls.Add(this.btnEditGateway);
            this.groupBoxControls.Controls.Add(this.btnAddGateway);
            this.groupBoxControls.Dock = System.Windows.Forms.DockStyle.Top;
            this.groupBoxControls.Location = new System.Drawing.Point(0, 0);
            this.groupBoxControls.Name = "groupBoxControls";
            this.groupBoxControls.Size = new System.Drawing.Size(300, 80);
            this.groupBoxControls.TabIndex = 0;
            this.groupBoxControls.TabStop = false;
            this.groupBoxControls.Text = "Controles";
            // 
            // btnExportNow
            // 
            this.btnExportNow.Location = new System.Drawing.Point(190, 45);
            this.btnExportNow.Name = "btnExportNow";
            this.btnExportNow.Size = new System.Drawing.Size(100, 30);
            this.btnExportNow.TabIndex = 5;
            this.btnExportNow.Text = "Exportar CSV";
            this.btnExportNow.UseVisualStyleBackColor = true;
            this.btnExportNow.Click += new System.EventHandler(this.BtnExportNow_Click);
            // 
            // btnStop
            // 
            this.btnStop.Enabled = false;
            this.btnStop.Location = new System.Drawing.Point(100, 45);
            this.btnStop.Name = "btnStop";
            this.btnStop.Size = new System.Drawing.Size(80, 30);
            this.btnStop.TabIndex = 4;
            this.btnStop.Text = "Detener";
            this.btnStop.UseVisualStyleBackColor = true;
            this.btnStop.Click += new System.EventHandler(this.BtnStop_Click);
            // 
            // btnStart
            // 
            this.btnStart.Location = new System.Drawing.Point(10, 45);
            this.btnStart.Name = "btnStart";
            this.btnStart.Size = new System.Drawing.Size(80, 30);
            this.btnStart.TabIndex = 3;
            this.btnStart.Text = "Iniciar";
            this.btnStart.UseVisualStyleBackColor = true;
            this.btnStart.Click += new System.EventHandler(this.BtnStart_Click);
            // 
            // btnDeleteGateway
            // 
            this.btnDeleteGateway.Enabled = false;
            this.btnDeleteGateway.Location = new System.Drawing.Point(210, 20);
            this.btnDeleteGateway.Name = "btnDeleteGateway";
            this.btnDeleteGateway.Size = new System.Drawing.Size(80, 25);
            this.btnDeleteGateway.TabIndex = 2;
            this.btnDeleteGateway.Text = "Eliminar";
            this.btnDeleteGateway.UseVisualStyleBackColor = true;
            this.btnDeleteGateway.Click += new System.EventHandler(this.BtnDeleteGateway_Click);
            // 
            // btnEditGateway
            // 
            this.btnEditGateway.Enabled = false;
            this.btnEditGateway.Location = new System.Drawing.Point(120, 20);
            this.btnEditGateway.Name = "btnEditGateway";
            this.btnEditGateway.Size = new System.Drawing.Size(80, 25);
            this.btnEditGateway.TabIndex = 1;
            this.btnEditGateway.Text = "Editar";
            this.btnEditGateway.UseVisualStyleBackColor = true;
            this.btnEditGateway.Click += new System.EventHandler(this.BtnEditGateway_Click);
            // 
            // btnAddGateway
            // 
            this.btnAddGateway.Location = new System.Drawing.Point(10, 20);
            this.btnAddGateway.Name = "btnAddGateway";
            this.btnAddGateway.Size = new System.Drawing.Size(100, 25);
            this.btnAddGateway.TabIndex = 0;
            this.btnAddGateway.Text = "Agregar Gateway";
            this.btnAddGateway.UseVisualStyleBackColor = true;
            this.btnAddGateway.Click += new System.EventHandler(this.BtnAddGateway_Click);
            // 
            // dataGridViewData
            // 
            this.dataGridViewData.AllowUserToAddRows = false;
            this.dataGridViewData.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataGridViewData.Location = new System.Drawing.Point(0, 0);
            this.dataGridViewData.Name = "dataGridViewData";
            this.dataGridViewData.ReadOnly = true;
            this.dataGridViewData.RowHeadersWidth = 51;
            this.dataGridViewData.RowTemplate.Height = 29;
            this.dataGridViewData.Size = new System.Drawing.Size(896, 690);
            this.dataGridViewData.TabIndex = 0;
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 20F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 771);
            this.Controls.Add(this.splitContainer);
            this.Controls.Add(this.statusStrip);
            this.Controls.Add(this.toolStrip);
            this.Controls.Add(this.menuStrip);
            this.Icon = System.Drawing.SystemIcons.Application;
            this.MainMenuStrip = this.menuStrip;
            this.Name = "MainForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "ProFace Data Extractor v1.0";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_FormClosing);
            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.toolStrip.ResumeLayout(false);
            this.toolStrip.PerformLayout();
            this.statusStrip.ResumeLayout(false);
            this.statusStrip.PerformLayout();
            this.splitContainer.Panel1.ResumeLayout(false);
            this.splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer)).EndInit();
            this.splitContainer.ResumeLayout(false);
            this.leftPanel.ResumeLayout(false);
            this.groupBoxControls.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewData)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.MenuStrip menuStrip;
        private System.Windows.Forms.ToolStripMenuItem archivoToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem exportarConfiguracionToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem importarConfiguracionToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem salirToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem configuracionToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem configuracionGeneralToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ayudaToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem acercaDeToolStripMenuItem;
        private System.Windows.Forms.ToolStrip toolStrip;
        private System.Windows.Forms.ToolStripButton btnToolStart;
        private System.Windows.Forms.ToolStripButton btnToolStop;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripButton btnToolExport;
        private System.Windows.Forms.StatusStrip statusStrip;
        private System.Windows.Forms.ToolStripStatusLabel statusLabel;
        private System.Windows.Forms.ToolStripStatusLabel updateLabel;
        private System.Windows.Forms.ToolStripProgressBar progressLabel;
        private System.Windows.Forms.SplitContainer splitContainer;
        private System.Windows.Forms.Panel leftPanel;
        private System.Windows.Forms.TreeView treeViewGateways;
        private System.Windows.Forms.GroupBox groupBoxControls;
        private System.Windows.Forms.Button btnExportNow;
        private System.Windows.Forms.Button btnStop;
        private System.Windows.Forms.Button btnStart;
        private System.Windows.Forms.Button btnDeleteGateway;
        private System.Windows.Forms.Button btnEditGateway;
        private System.Windows.Forms.Button btnAddGateway;
        private System.Windows.Forms.DataGridView dataGridViewData;
    }
}
