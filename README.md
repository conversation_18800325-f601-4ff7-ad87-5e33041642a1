# ProFace Data Extractor v1.0

## Descripción

ProFace Data Extractor es una aplicación Windows Forms desarrollada en .NET 6 que permite conectar y configurar múltiples pasarelas ProFace GP5000 (HMI) para extraer datos de PLCs usando el protocolo Modbus TCP/IP. La aplicación almacena los datos tanto en archivos CSV como en base de datos SQL Server.

## Características Principales

### ✅ Funcionalidades Implementadas

- **Gestión de Pasarelas ProFace GP5000**
  - Agregar, editar y eliminar pasarelas
  - Configuración de IP, puerto y parámetros de comunicación
  - Prueba de conexión en tiempo real
  - Reconexión automática en caso de error

- **Gestión de PLCs**
  - Configuración de múltiples PLCs por pasarela
  - Soporte para diferentes marcas y modelos
  - Configuración de Slave ID y parámetros específicos

- **Configuración de Variables**
  - Definición de puntos de datos (DataPoints)
  - Soporte para diferentes tipos de datos (Int, Float, Bool, etc.)
  - Configuración de direcciones Modbus
  - Factor de escala y offset personalizable

- **Almacenamiento de Datos**
  - Exportación automática y manual a CSV
  - Almacenamiento en base de datos SQL Server
  - Limpieza automática de datos antiguos
  - Compresión de archivos antiguos

- **Interfaz de Usuario**
  - Interfaz moderna compatible con Visual Studio Designer
  - TreeView para navegación de pasarelas y PLCs
  - DataGridView para visualización de datos en tiempo real
  - Barra de estado con información de conexión

- **Logging y Monitoreo**
  - Sistema de logging avanzado con Serilog
  - Logs rotativos con retención configurable
  - Manejo centralizado de excepciones
  - Estadísticas de errores y rendimiento

## Requisitos del Sistema

- Windows 10/11 o Windows Server 2016+
- .NET 6.0 Runtime
- SQL Server 2016+ (opcional, para almacenamiento en BD)
- Visual Studio 2022 (para desarrollo)

## Instalación

### Para Usuarios Finales

1. Descargar el release más reciente
2. Extraer el archivo ZIP
3. Ejecutar `ProFaceDataExtractor.exe`

### Para Desarrolladores

1. Clonar el repositorio:
   ```bash
   git clone [URL_DEL_REPOSITORIO]
   cd ProFaceDataExtractor
   ```

2. Restaurar paquetes NuGet:
   ```bash
   dotnet restore
   ```

3. Compilar la aplicación:
   ```bash
   dotnet build
   ```

4. Ejecutar la aplicación:
   ```bash
   dotnet run
   ```

## Configuración

### Configuración Básica

1. **Agregar Pasarela**:
   - Hacer clic en "Agregar Gateway"
   - Configurar IP, puerto y parámetros de conexión
   - Probar la conexión antes de guardar

2. **Configurar PLCs**:
   - Seleccionar la pasarela en el TreeView
   - Agregar PLCs con sus respectivos Slave IDs
   - Configurar marca, modelo y descripción

3. **Definir Variables**:
   - Para cada PLC, definir los puntos de datos
   - Especificar direcciones Modbus (ej: 40001, HR1, etc.)
   - Configurar tipo de dato y factor de escala

### Configuración de Base de Datos

1. **Crear Base de Datos**:
   ```sql
   CREATE DATABASE ProFaceDataExtractor;
   ```

2. **Ejecutar Script de Tablas**:
   - Usar el archivo `Scripts/CreateDatabaseTables.sql`
   - Ejecutar en SQL Server Management Studio

3. **Configurar Connection String**:
   - En la aplicación, ir a Configuración → Configuración General
   - Habilitar base de datos
   - Configurar cadena de conexión:
     ```
     Server=localhost;Database=ProFaceDataExtractor;Integrated Security=true;
     ```

## Uso de la Aplicación

### Operación Normal

1. **Iniciar Lectura**:
   - Hacer clic en "Iniciar" o usar Ctrl+S
   - La aplicación comenzará a leer datos automáticamente
   - El estado se muestra en la barra inferior

2. **Monitorear Datos**:
   - Los datos se muestran en tiempo real en el DataGridView
   - El TreeView muestra el estado de conexión de cada elemento

3. **Exportar Datos**:
   - Exportación automática según intervalo configurado
   - Exportación manual con "Exportar CSV"
   - Los archivos se guardan en la carpeta `Exports`

### Solución de Problemas

- **Error de Conexión**: Verificar IP, puerto y conectividad de red
- **Datos Incorrectos**: Revisar direcciones Modbus y configuración de PLC
- **Rendimiento Lento**: Ajustar intervalos de lectura y número de variables

## Arquitectura del Código

### Estructura de Carpetas

```
ProFaceDataExtractor/
├── Forms/                  # Formularios de la aplicación
│   ├── MainForm.cs        # Formulario principal
│   ├── GatewayConfigForm.cs # Configuración de pasarelas
│   └── PLCConfigForm.cs   # Configuración de PLCs
├── Models/                # Modelos de datos
│   ├── ApplicationSettings.cs
│   ├── GatewayConfiguration.cs
│   ├── PLCConfiguration.cs
│   ├── DataPoint.cs
│   └── DatabaseModels.cs
├── Services/              # Servicios de negocio
│   ├── ModbusCommunicationService.cs
│   ├── ConfigurationService.cs
│   ├── CSVExportService.cs
│   ├── DatabaseService.cs
│   ├── DataStorageService.cs
│   └── ExceptionHandlingService.cs
├── Utils/                 # Utilidades
│   ├── ValidationHelper.cs
│   └── SerializationHelper.cs
├── Scripts/               # Scripts de base de datos
└── Logs/                  # Archivos de log
```

### Patrones de Diseño Utilizados

- **Dependency Injection**: Para gestión de servicios
- **Repository Pattern**: Para acceso a datos
- **Observer Pattern**: Para notificaciones de estado
- **Strategy Pattern**: Para diferentes tipos de almacenamiento

## Desarrollo y Extensión

### Agregar Nuevo Tipo de PLC

1. Extender `PLCConfiguration` con propiedades específicas
2. Modificar `ModbusCommunicationService` para el protocolo específico
3. Actualizar formularios de configuración

### Agregar Nuevo Formato de Exportación

1. Crear nuevo servicio que implemente `IExportService`
2. Registrar en el contenedor DI
3. Agregar opción en la interfaz de usuario

### Personalizar Logging

```csharp
Log.Logger = new LoggerConfiguration()
    .WriteTo.File("custom-log.txt")
    .WriteTo.Console()
    .CreateLogger();
```

## Licencia

[Especificar licencia aquí]

## Soporte

Para soporte técnico o reportar bugs:
- Email: [email de soporte]
- Issues: [URL del repositorio]/issues

## Changelog

### v1.0.0 (2025-07-02)
- Implementación inicial
- Soporte para Modbus TCP/IP
- Almacenamiento en CSV y SQL Server
- Interfaz compatible con Visual Studio Designer
- Sistema de logging avanzado
- Reconexión automática
- Manejo robusto de errores

---

**Desarrollado con ❤️ para la industria 4.0**
