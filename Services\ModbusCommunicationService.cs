using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EasyModbus;
using ProFaceDataExtractor.Models;
using Serilog;

namespace ProFaceDataExtractor.Services
{
    public class ModbusCommunicationService
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, ModbusClient> _clients;
        private readonly Dictionary<string, GatewayConfiguration> _gatewayConfigs;
        private readonly Dictionary<string, DateTime> _lastConnectionAttempt;

        public ModbusCommunicationService(ILogger logger)
        {
            _logger = logger;
            _clients = new Dictionary<string, ModbusClient>();
            _gatewayConfigs = new Dictionary<string, GatewayConfiguration>();
            _lastConnectionAttempt = new Dictionary<string, DateTime>();
        }

        public async Task<bool> ConnectToGatewayAsync(GatewayConfiguration gateway)
        {
            var key = $"{gateway.IpAddress}:{gateway.Port}";
            var retryCount = 0;

            // Guardar configuración para reconexión automática
            _gatewayConfigs[key] = gateway;

            while (retryCount <= gateway.RetryCount)
            {
                try
                {
                    // Desconectar cliente existente si existe
                    if (_clients.ContainsKey(key))
                    {
                        try
                        {
                            _clients[key].Disconnect();
                        }
                        catch { }
                        _clients.Remove(key);
                    }

                    var client = new ModbusClient(gateway.IpAddress, gateway.Port);
                    client.ConnectionTimeout = gateway.Timeout;

                    await Task.Run(() => client.Connect());

                    if (client.Connected)
                    {
                        _clients[key] = client;
                        _lastConnectionAttempt[key] = DateTime.Now;
                        _logger.Information($"Conectado exitosamente a Gateway {gateway.Name} ({gateway.IpAddress}:{gateway.Port})");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warning($"Intento {retryCount + 1} de conexión a Gateway {gateway.Name} falló: {ex.Message}");
                    retryCount++;

                    if (retryCount <= gateway.RetryCount)
                    {
                        await Task.Delay(1000 * retryCount); // Delay incremental
                    }
                }
            }

            _logger.Error($"No se pudo conectar a Gateway {gateway.Name} después de {gateway.RetryCount + 1} intentos");
            return false;
        }

        public async Task<Dictionary<string, object>> ReadDataPointsAsync(GatewayConfiguration gateway, PLCConfiguration plc)
        {
            var results = new Dictionary<string, object>();
            var key = $"{gateway.IpAddress}:{gateway.Port}";

            // Verificar y reconectar si es necesario
            if (!await EnsureConnectionAsync(gateway))
            {
                _logger.Warning($"No se pudo establecer conexión con {gateway.Name}");
                return results;
            }

            var client = _clients[key];

            try
            {
                foreach (var dataPoint in plc.DataPoints)
                {
                    if (!dataPoint.IsEnabled) continue;

                    var value = await ReadSingleDataPointAsync(client, dataPoint, int.Parse(plc.SlaveId));
                    if (value != null)
                    {
                        // Aplicar factor de escala y offset
                        if (value is int intValue)
                        {
                            value = (intValue * dataPoint.ScaleFactor) + dataPoint.Offset;
                        }
                        else if (value is float floatValue)
                        {
                            value = (floatValue * dataPoint.ScaleFactor) + dataPoint.Offset;
                        }

                        results[dataPoint.Name] = value;
                        dataPoint.LastValue = value;
                        dataPoint.LastReadTime = DateTime.Now;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error leyendo datos del PLC {plc.Name}: {ex.Message}");

                // Intentar reconectar en caso de error de comunicación
                if (ex.Message.Contains("connection") || ex.Message.Contains("timeout"))
                {
                    _logger.Information($"Intentando reconectar a {gateway.Name} debido a error de comunicación");
                    _ = Task.Run(() => ConnectToGatewayAsync(gateway));
                }
            }

            return results;
        }

        private async Task<object?> ReadSingleDataPointAsync(ModbusClient client, DataPoint dataPoint, int slaveId)
        {
            try
            {
                // Parsear la dirección (formato: tipo+dirección, ej: "40001" para holding register)
                var address = ParseAddress(dataPoint.Address);

                return dataPoint.DataType switch
                {
                    DataType.Int16 => await Task.Run(() => client.ReadHoldingRegisters(address, 1)[0]),
                    DataType.Int32 => await Task.Run(() => {
                        var registers = client.ReadHoldingRegisters(address, 2);
                        return (registers[0] << 16) | registers[1];
                    }),
                    DataType.Float => await Task.Run(() => {
                        var registers = client.ReadHoldingRegisters(address, 2);
                        var bytes = new byte[4];
                        BitConverter.GetBytes(registers[0]).CopyTo(bytes, 0);
                        BitConverter.GetBytes(registers[1]).CopyTo(bytes, 2);
                        return BitConverter.ToSingle(bytes, 0);
                    }),
                    DataType.Bool => await Task.Run(() => client.ReadCoils(address, 1)[0]),
                    _ => null
                };
            }
            catch (Exception ex)
            {
                _logger.Warning($"Error leyendo punto de datos {dataPoint.Name}: {ex.Message}");
                return null;
            }
        }

        private async Task<bool> EnsureConnectionAsync(GatewayConfiguration gateway)
        {
            var key = $"{gateway.IpAddress}:{gateway.Port}";

            // Verificar si ya está conectado
            if (_clients.ContainsKey(key) && _clients[key].Connected)
            {
                return true;
            }

            // Verificar si no hemos intentado reconectar recientemente (evitar spam de reconexiones)
            if (_lastConnectionAttempt.ContainsKey(key) &&
                DateTime.Now - _lastConnectionAttempt[key] < TimeSpan.FromSeconds(30))
            {
                return false;
            }

            // Intentar reconectar
            return await ConnectToGatewayAsync(gateway);
        }

        private int ParseAddress(string address)
        {
            // Manejo mejorado de direcciones Modbus
            if (string.IsNullOrEmpty(address))
                return 0;

            // Remover espacios y convertir a mayúsculas
            address = address.Trim().ToUpper();

            // Formato numérico directo
            if (int.TryParse(address, out int numericResult))
            {
                return numericResult;
            }

            // Formato con prefijo (HR1, IR1, etc.)
            if (address.Length > 2)
            {
                var prefix = address.Substring(0, 2);
                var numberPart = address.Substring(2);

                if (int.TryParse(numberPart, out int addressNumber))
                {
                    return prefix switch
                    {
                        "HR" => 40000 + addressNumber, // Holding Registers
                        "IR" => 30000 + addressNumber, // Input Registers
                        "DI" => 10000 + addressNumber, // Discrete Inputs
                        "CO" => addressNumber,          // Coils
                        _ => addressNumber
                    };
                }
            }

            _logger.Warning($"No se pudo parsear la dirección Modbus: {address}");
            return 0;
        }

        public void DisconnectAll()
        {
            foreach (var client in _clients.Values)
            {
                try
                {
                    if (client.Connected)
                    {
                        client.Disconnect();
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warning($"Error desconectando cliente Modbus: {ex.Message}");
                }
            }
            _clients.Clear();
            _gatewayConfigs.Clear();
            _lastConnectionAttempt.Clear();
            _logger.Information("Todas las conexiones Modbus han sido desconectadas");
        }

        public bool IsConnected(GatewayConfiguration gateway)
        {
            var key = $"{gateway.IpAddress}:{gateway.Port}";
            return _clients.ContainsKey(key) && _clients[key].Connected;
        }
    }
}