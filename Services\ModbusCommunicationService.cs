using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using EasyModbus;
using ProFaceDataExtractor.Models;
using Serilog;

namespace ProFaceDataExtractor.Services
{
    public class ModbusCommunicationService
    {
        private readonly ILogger _logger;
        private readonly Dictionary<string, ModbusClient> _clients;

        public ModbusCommunicationService(ILogger logger)
        {
            _logger = logger;
            _clients = new Dictionary<string, ModbusClient>();
        }

        public async Task<bool> ConnectToGatewayAsync(GatewayConfiguration gateway)
        {
            try
            {
                var key = $"{gateway.IpAddress}:{gateway.Port}";

                if (_clients.ContainsKey(key))
                {
                    _clients[key].Disconnect();
                    _clients.Remove(key);
                }

                var client = new ModbusClient(gateway.IpAddress, gateway.Port);
                client.ConnectionTimeout = gateway.Timeout;

                await Task.Run(() => client.Connect());
                _clients[key] = client;

                _logger.Information($"Conectado exitosamente a Gateway {gateway.Name} ({gateway.IpAddress}:{gateway.Port})");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error conectando a Gateway {gateway.Name}: {ex.Message}");
                return false;
            }
        }

        public async Task<Dictionary<string, object>> ReadDataPointsAsync(GatewayConfiguration gateway, PLCConfiguration plc)
        {
            var results = new Dictionary<string, object>();
            var key = $"{gateway.IpAddress}:{gateway.Port}";

            if (!_clients.ContainsKey(key))
            {
                _logger.Warning($"No hay conexión activa con {gateway.Name}");
                return results;
            }

            var client = _clients[key];

            try
            {
                foreach (var dataPoint in plc.DataPoints)
                {
                    if (!dataPoint.IsEnabled) continue;

                    var value = await ReadSingleDataPointAsync(client, dataPoint, int.Parse(plc.SlaveId));
                    if (value != null)
                    {
                        // Aplicar factor de escala y offset
                        if (value is int intValue)
                        {
                            value = (intValue * dataPoint.ScaleFactor) + dataPoint.Offset;
                        }
                        else if (value is float floatValue)
                        {
                            value = (floatValue * dataPoint.ScaleFactor) + dataPoint.Offset;
                        }

                        results[dataPoint.Name] = value;
                        dataPoint.LastValue = value;
                        dataPoint.LastReadTime = DateTime.Now;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error leyendo datos del PLC {plc.Name}: {ex.Message}");
            }

            return results;
        }

        private async Task<object?> ReadSingleDataPointAsync(ModbusClient client, DataPoint dataPoint, int slaveId)
        {
            try
            {
                // Parsear la dirección (formato: tipo+dirección, ej: "40001" para holding register)
                var address = ParseAddress(dataPoint.Address);

                return dataPoint.DataType switch
                {
                    DataType.Int16 => await Task.Run(() => client.ReadHoldingRegisters(address, 1)[0]),
                    DataType.Int32 => await Task.Run(() => {
                        var registers = client.ReadHoldingRegisters(address, 2);
                        return (registers[0] << 16) | registers[1];
                    }),
                    DataType.Float => await Task.Run(() => {
                        var registers = client.ReadHoldingRegisters(address, 2);
                        var bytes = new byte[4];
                        BitConverter.GetBytes(registers[0]).CopyTo(bytes, 0);
                        BitConverter.GetBytes(registers[1]).CopyTo(bytes, 2);
                        return BitConverter.ToSingle(bytes, 0);
                    }),
                    DataType.Bool => await Task.Run(() => client.ReadCoils(address, 1)[0]),
                    _ => null
                };
            }
            catch (Exception ex)
            {
                _logger.Warning($"Error leyendo punto de datos {dataPoint.Name}: {ex.Message}");
                return null;
            }
        }

        private int ParseAddress(string address)
        {
            // Implementación simple - en producción debería ser más robusta
            if (int.TryParse(address, out int result))
            {
                return result;
            }
            return 0;
        }

        public void DisconnectAll()
        {
            foreach (var client in _clients.Values)
            {
                try
                {
                    client.Disconnect();
                }
                catch (Exception ex)
                {
                    _logger.Warning($"Error desconectando cliente Modbus: {ex.Message}");
                }
            }
            _clients.Clear();
        }

        public bool IsConnected(GatewayConfiguration gateway)
        {
            var key = $"{gateway.IpAddress}:{gateway.Port}";
            return _clients.ContainsKey(key) && _clients[key].Connected;
        }
    }
}