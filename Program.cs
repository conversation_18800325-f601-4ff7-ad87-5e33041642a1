using System;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using ProFaceDataExtractor.Forms;
using ProFaceDataExtractor.Services;
using Serilog;

namespace ProFaceDataExtractor
{
    internal static class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                // Configurar Serilog con configuración avanzada
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Information()
                    .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
                    .MinimumLevel.Override("System", Serilog.Events.LogEventLevel.Warning)
                    .Enrich.FromLogContext()
                    .Enrich.WithMachineName()
                    .Enrich.WithEnvironmentUserName()
                    .Enrich.WithProcessId()
                    .Enrich.WithThreadId()
                    .WriteTo.File("./Logs/app-.log",
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: 30,
                        fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB
                        rollOnFileSizeLimit: true,
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                    .WriteTo.File("./Logs/errors-.log",
                        restrictedToMinimumLevel: Serilog.Events.LogEventLevel.Warning,
                        rollingInterval: RollingInterval.Day,
                        retainedFileCountLimit: 90,
                        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
                    .WriteTo.Console(outputTemplate: "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
                    .CreateLogger();

                Log.Information("=== Iniciando aplicación ProFace Data Extractor v1.0 ===");
                Log.Information("Máquina: {MachineName}, Usuario: {UserName}", Environment.MachineName, Environment.UserName);

                // Configurar servicios DI
                var services = new ServiceCollection();
                ConfigureServices(services);
                var serviceProvider = services.BuildServiceProvider();

                ApplicationConfiguration.Initialize();
                Application.Run(new MainForm(serviceProvider));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error fatal al iniciar la aplicación: {ex.Message}\n\nDetalles: {ex}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Log.Fatal(ex, "Error fatal al iniciar la aplicación");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        private static void ConfigureServices(ServiceCollection services)
        {
            services.AddSingleton<ILogger>(Log.Logger);
            services.AddSingleton<ModbusCommunicationService>();
            services.AddSingleton<CSVExportService>();
            services.AddSingleton<ConfigurationService>();

            // Configurar DatabaseService si está habilitado
            services.AddSingleton<DatabaseService>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger>();
                var configService = provider.GetRequiredService<ConfigurationService>();

                // Cargar configuración para obtener connection string
                var settings = configService.LoadSettingsAsync().Result;

                if (settings.DatabaseEnabled && !string.IsNullOrEmpty(settings.DatabaseConnectionString))
                {
                    return new DatabaseService(logger, settings.DatabaseConnectionString);
                }

                // Retornar null si la base de datos no está habilitada
                return null!;
            });

            services.AddSingleton<DataStorageService>();
        }
    }
}