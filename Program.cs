using System;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using ProFaceDataExtractor.Forms;
using ProFaceDataExtractor.Services;
using Serilog;

namespace ProFaceDataExtractor
{
    internal static class Program
    {
        [STAThread]
        static void Main()
        {
            // Configurar Serilog
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.File("./Logs/app.log", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            // Configurar servicios DI
            var services = new ServiceCollection();
            ConfigureServices(services);
            var serviceProvider = services.BuildServiceProvider();

            ApplicationConfiguration.Initialize();
            Application.Run(new MainForm(serviceProvider));
        }

        private static void ConfigureServices(ServiceCollection services)
        {
            services.AddSingleton<ILogger>(Log.Logger);
            services.AddSingleton<ModbusCommunicationService>();
            services.AddSingleton<CSVExportService>();
            services.AddSingleton<ConfigurationService>();
        }
    }
}