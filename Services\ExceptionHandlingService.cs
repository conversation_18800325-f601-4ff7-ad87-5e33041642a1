using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using ProFaceDataExtractor.Models.Database;
using Serilog;

namespace ProFaceDataExtractor.Services
{
    /// <summary>
    /// Servicio centralizado para manejo de excepciones y logging
    /// </summary>
    public class ExceptionHandlingService
    {
        private readonly ILogger _logger;
        private readonly DatabaseService? _databaseService;

        public ExceptionHandlingService(ILogger logger, DatabaseService? databaseService = null)
        {
            _logger = logger;
            _databaseService = databaseService;
        }

        /// <summary>
        /// Maneja una excepción de forma centralizada
        /// </summary>
        public async Task HandleExceptionAsync(
            Exception exception, 
            string context = "", 
            string category = "General",
            bool showToUser = false,
            string userMessage = "")
        {
            try
            {
                // Log detallado de la excepción
                _logger.Error(exception, "Excepción en {Context}: {Message}", context, exception.Message);

                // Crear entrada de log para base de datos
                var eventLog = new EventLogEntity
                {
                    Level = "Error",
                    Message = $"[{context}] {exception.Message}",
                    Exception = exception.ToString(),
                    Source = context,
                    Category = category,
                    Timestamp = DateTime.Now
                };

                // Guardar en base de datos si está disponible
                if (_databaseService != null)
                {
                    await _databaseService.InsertEventLogAsync(eventLog);
                }

                // Mostrar al usuario si es necesario
                if (showToUser)
                {
                    var displayMessage = string.IsNullOrEmpty(userMessage) 
                        ? $"Error en {context}: {exception.Message}" 
                        : userMessage;
                    
                    MessageBox.Show(displayMessage, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception loggingException)
            {
                // Si falla el logging, al menos intentar escribir a consola
                Console.WriteLine($"Error en logging: {loggingException.Message}");
                Console.WriteLine($"Excepción original: {exception.Message}");
            }
        }

        /// <summary>
        /// Maneja una advertencia
        /// </summary>
        public async Task HandleWarningAsync(
            string message, 
            string context = "", 
            string category = "General",
            bool showToUser = false)
        {
            try
            {
                _logger.Warning("[{Context}] {Message}", context, message);

                var eventLog = new EventLogEntity
                {
                    Level = "Warning",
                    Message = $"[{context}] {message}",
                    Source = context,
                    Category = category,
                    Timestamp = DateTime.Now
                };

                if (_databaseService != null)
                {
                    await _databaseService.InsertEventLogAsync(eventLog);
                }

                if (showToUser)
                {
                    MessageBox.Show(message, "Advertencia", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error en logging de advertencia: {ex.Message}");
            }
        }

        /// <summary>
        /// Registra información importante
        /// </summary>
        public async Task LogInformationAsync(
            string message, 
            string context = "", 
            string category = "General")
        {
            try
            {
                _logger.Information("[{Context}] {Message}", context, message);

                var eventLog = new EventLogEntity
                {
                    Level = "Information",
                    Message = $"[{context}] {message}",
                    Source = context,
                    Category = category,
                    Timestamp = DateTime.Now
                };

                if (_databaseService != null)
                {
                    await _databaseService.InsertEventLogAsync(eventLog);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error en logging de información: {ex.Message}");
            }
        }

        /// <summary>
        /// Ejecuta una acción con manejo automático de excepciones
        /// </summary>
        public async Task<T?> ExecuteWithHandlingAsync<T>(
            Func<Task<T>> action,
            string context,
            string category = "General",
            bool showErrorToUser = false,
            string userErrorMessage = "",
            T? defaultValue = default)
        {
            try
            {
                return await action();
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, context, category, showErrorToUser, userErrorMessage);
                return defaultValue;
            }
        }

        /// <summary>
        /// Ejecuta una acción con manejo automático de excepciones (sin retorno)
        /// </summary>
        public async Task ExecuteWithHandlingAsync(
            Func<Task> action,
            string context,
            string category = "General",
            bool showErrorToUser = false,
            string userErrorMessage = "")
        {
            try
            {
                await action();
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, context, category, showErrorToUser, userErrorMessage);
            }
        }

        /// <summary>
        /// Ejecuta una acción síncrona con manejo automático de excepciones
        /// </summary>
        public async Task<T?> ExecuteWithHandlingAsync<T>(
            Func<T> action,
            string context,
            string category = "General",
            bool showErrorToUser = false,
            string userErrorMessage = "",
            T? defaultValue = default)
        {
            try
            {
                return action();
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, context, category, showErrorToUser, userErrorMessage);
                return defaultValue;
            }
        }

        /// <summary>
        /// Ejecuta una acción síncrona con manejo automático de excepciones (sin retorno)
        /// </summary>
        public async Task ExecuteWithHandlingAsync(
            Action action,
            string context,
            string category = "General",
            bool showErrorToUser = false,
            string userErrorMessage = "")
        {
            try
            {
                action();
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, context, category, showErrorToUser, userErrorMessage);
            }
        }

        /// <summary>
        /// Configura el manejo global de excepciones no controladas
        /// </summary>
        public void SetupGlobalExceptionHandling()
        {
            // Manejo de excepciones no controladas en el hilo principal
            Application.ThreadException += async (sender, e) =>
            {
                await HandleExceptionAsync(e.Exception, "Application.ThreadException", "Critical", true,
                    "Se ha producido un error inesperado en la aplicación. La aplicación intentará continuar funcionando.");
            };

            // Manejo de excepciones no controladas en otros hilos
            AppDomain.CurrentDomain.UnhandledException += async (sender, e) =>
            {
                var exception = e.ExceptionObject as Exception ?? new Exception("Excepción desconocida");
                await HandleExceptionAsync(exception, "AppDomain.UnhandledException", "Critical", true,
                    "Se ha producido un error crítico. La aplicación debe cerrarse.");
            };

            // Manejo de excepciones no observadas en tareas
            TaskScheduler.UnobservedTaskException += async (sender, e) =>
            {
                await HandleExceptionAsync(e.Exception, "TaskScheduler.UnobservedTaskException", "Critical");
                e.SetObserved(); // Marcar como observada para evitar que termine la aplicación
            };

            _logger.Information("Configurado manejo global de excepciones");
        }

        /// <summary>
        /// Obtiene estadísticas de errores
        /// </summary>
        public async Task<Dictionary<string, object>> GetErrorStatisticsAsync(int days = 7)
        {
            var stats = new Dictionary<string, object>();

            try
            {
                if (_databaseService != null)
                {
                    // Aquí podrías implementar consultas para obtener estadísticas de errores
                    // Por ahora, retornamos estadísticas básicas
                    stats["DatabaseLoggingEnabled"] = true;
                    stats["Period"] = $"Últimos {days} días";
                    stats["GeneratedAt"] = DateTime.Now;
                }
                else
                {
                    stats["DatabaseLoggingEnabled"] = false;
                    stats["Message"] = "Logging a base de datos no está habilitado";
                }
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(ex, "GetErrorStatistics", "Statistics");
                stats["Error"] = ex.Message;
            }

            return stats;
        }
    }
}
