-- Script para crear la base de datos ProFaceData
-- Ejecutar en SQL Server Management Studio

-- Crear base de datos si no existe
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ProFaceData')
BEGIN
    CREATE DATABASE ProFaceData;
    PRINT 'Base de datos ProFaceData creada exitosamente.';
END
ELSE
BEGIN
    PRINT 'La base de datos ProFaceData ya existe.';
END

-- Usar la base de datos
USE ProFaceData;

-- Crear tabla ModbusData si no existe
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ModbusData' AND xtype='U')
BEGIN
    CREATE TABLE ModbusData (
        Id int IDENTITY(1,1) PRIMARY KEY,
        Timestamp datetime NOT NULL DEFAULT GETDATE(),
        Address nvarchar(50) NOT NULL,
        Value int NOT NULL,
        Description nvarchar(200),
        DeviceIP nvarchar(15),
        CreatedAt datetime NOT NULL DEFAULT GETDATE()
    );
    
    -- <PERSON><PERSON>r índices para mejorar rendimiento
    CREATE INDEX IX_ModbusData_Timestamp ON ModbusData(Timestamp);
    CREATE INDEX IX_ModbusData_Address ON ModbusData(Address);
    
    PRINT 'Tabla ModbusData creada exitosamente con índices.';
END
ELSE
BEGIN
    PRINT 'La tabla ModbusData ya existe.';
END

-- Insertar datos de ejemplo (opcional)
INSERT INTO ModbusData (Address, Value, Description, DeviceIP)
VALUES 
    ('40001', 1234, 'Registro de prueba 1', '*************'),
    ('40002', 5678, 'Registro de prueba 2', '*************'),
    ('40003', 9012, 'Registro de prueba 3', '*************');

PRINT 'Datos de ejemplo insertados.';

-- Mostrar estructura de la tabla
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'ModbusData'
ORDER BY ORDINAL_POSITION;

-- Mostrar datos existentes
SELECT TOP 10 * FROM ModbusData ORDER BY CreatedAt DESC;

PRINT 'Script completado exitosamente.';
