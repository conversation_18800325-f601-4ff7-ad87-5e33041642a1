-- Script para crear las tablas de la base de datos ProFace Data Extractor
-- Ejecutar en SQL Server Management Studio o similar

USE [ProFaceDataExtractor] -- Cambiar por el nombre de tu base de datos
GO

-- Crear tabla Gateways si no existe
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Gateways' AND xtype='U')
BEGIN
    CREATE TABLE Gateways (
        Id NVARCHAR(50) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        IpAddress NVARCHAR(15) NOT NULL,
        Port INT NOT NULL,
        Timeout INT NOT NULL,
        RetryCount INT NOT NULL,
        IsEnabled BIT NOT NULL,
        Description NVARCHAR(500),
        CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        LastModified DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    PRINT 'Tabla Gateways creada exitosamente';
END
ELSE
    PRINT 'Tabla Gateways ya existe';

-- Crear tabla PLCs si no existe
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PLCs' AND xtype='U')
BEGIN
    CREATE TABLE PLCs (
        Id NVARCHAR(50) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        GatewayId NVARCHAR(50) NOT NULL,
        Brand NVARCHAR(50),
        Model NVARCHAR(100),
        SlaveId NVARCHAR(10) NOT NULL,
        IsEnabled BIT NOT NULL,
        Description NVARCHAR(500),
        CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        LastModified DATETIME2 NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (GatewayId) REFERENCES Gateways(Id)
    );
    PRINT 'Tabla PLCs creada exitosamente';
END
ELSE
    PRINT 'Tabla PLCs ya existe';

-- Crear tabla DataPoints si no existe
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DataPoints' AND xtype='U')
BEGIN
    CREATE TABLE DataPoints (
        Id NVARCHAR(50) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        PLCId NVARCHAR(50) NOT NULL,
        Address NVARCHAR(20) NOT NULL,
        DataType NVARCHAR(20) NOT NULL,
        IsEnabled BIT NOT NULL,
        ScaleFactor FLOAT NOT NULL DEFAULT 1.0,
        Offset FLOAT NOT NULL DEFAULT 0.0,
        Unit NVARCHAR(20),
        Description NVARCHAR(500),
        CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
        LastModified DATETIME2 NOT NULL DEFAULT GETDATE(),
        FOREIGN KEY (PLCId) REFERENCES PLCs(Id)
    );
    PRINT 'Tabla DataPoints creada exitosamente';
END
ELSE
    PRINT 'Tabla DataPoints ya existe';

-- Crear tabla DataHistory si no existe
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DataHistory' AND xtype='U')
BEGIN
    CREATE TABLE DataHistory (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        DataPointId NVARCHAR(50) NOT NULL,
        PLCId NVARCHAR(50) NOT NULL,
        GatewayId NVARCHAR(50) NOT NULL,
        DataPointName NVARCHAR(100),
        PLCName NVARCHAR(100),
        GatewayName NVARCHAR(100),
        Value NVARCHAR(MAX),
        DataType NVARCHAR(20),
        NumericValue FLOAT NULL,
        BooleanValue BIT NULL,
        Unit NVARCHAR(20),
        Timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
        ReadTime DATETIME2 NOT NULL DEFAULT GETDATE(),
        IsValid BIT NOT NULL DEFAULT 1,
        ErrorMessage NVARCHAR(500),
        FOREIGN KEY (DataPointId) REFERENCES DataPoints(Id),
        FOREIGN KEY (PLCId) REFERENCES PLCs(Id),
        FOREIGN KEY (GatewayId) REFERENCES Gateways(Id)
    );
    PRINT 'Tabla DataHistory creada exitosamente';
END
ELSE
    PRINT 'Tabla DataHistory ya existe';

-- Crear índices para DataHistory
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_DataHistory_ReadTime')
BEGIN
    CREATE INDEX IX_DataHistory_ReadTime ON DataHistory(ReadTime);
    PRINT 'Índice IX_DataHistory_ReadTime creado exitosamente';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_DataHistory_GatewayId')
BEGIN
    CREATE INDEX IX_DataHistory_GatewayId ON DataHistory(GatewayId);
    PRINT 'Índice IX_DataHistory_GatewayId creado exitosamente';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_DataHistory_PLCId')
BEGIN
    CREATE INDEX IX_DataHistory_PLCId ON DataHistory(PLCId);
    PRINT 'Índice IX_DataHistory_PLCId creado exitosamente';
END

-- Crear tabla ApplicationConfig si no existe
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ApplicationConfig' AND xtype='U')
BEGIN
    CREATE TABLE ApplicationConfig (
        [Key] NVARCHAR(100) PRIMARY KEY,
        Value NVARCHAR(MAX),
        Description NVARCHAR(500),
        LastModified DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    PRINT 'Tabla ApplicationConfig creada exitosamente';
END
ELSE
    PRINT 'Tabla ApplicationConfig ya existe';

-- Crear tabla EventLogs si no existe
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EventLogs' AND xtype='U')
BEGIN
    CREATE TABLE EventLogs (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        Level NVARCHAR(20) NOT NULL,
        Message NVARCHAR(MAX) NOT NULL,
        Exception NVARCHAR(MAX),
        Source NVARCHAR(100),
        Category NVARCHAR(100),
        Timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
        MachineName NVARCHAR(50),
        UserName NVARCHAR(50)
    );
    PRINT 'Tabla EventLogs creada exitosamente';
END
ELSE
    PRINT 'Tabla EventLogs ya existe';

-- Crear índice para EventLogs
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_EventLogs_Timestamp')
BEGIN
    CREATE INDEX IX_EventLogs_Timestamp ON EventLogs(Timestamp);
    PRINT 'Índice IX_EventLogs_Timestamp creado exitosamente';
END

-- Insertar configuración inicial si no existe
IF NOT EXISTS (SELECT * FROM ApplicationConfig WHERE [Key] = 'DatabaseVersion')
BEGIN
    INSERT INTO ApplicationConfig ([Key], Value, Description)
    VALUES ('DatabaseVersion', '1.0', 'Versión inicial de la base de datos');
    PRINT 'Configuración inicial insertada';
END

PRINT 'Script de creación de base de datos completado exitosamente';
GO
