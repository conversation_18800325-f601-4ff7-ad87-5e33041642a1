using System;
using System.Net;
using System.Text.RegularExpressions;

namespace ProFaceDataExtractor.Utils
{
    public static class ValidationHelper
    {
        public static bool IsValidIpAddress(string ipAddress)
        {
            return IPAddress.TryParse(ipAddress, out _);
        }

        public static bool IsValidPort(int port)
        {
            return port > 0 && port <= 65535;
        }

        public static bool IsValidModbusAddress(string address)
        {
            // Validación básica para direcciones Modbus
            if (string.IsNullOrEmpty(address))
                return false;

            // Permite formato numérico simple (ej: 40001, 30001)
            if (int.TryParse(address, out int numAddress))
            {
                return numAddress >= 0 && numAddress <= 65535;
            }

            // Permite formato con prefijo (ej: HR1, IR1, etc)
            var pattern = @"^[A-Z]{1,2}\d{1,5}$";
            return Regex.IsMatch(address.ToUpper(), pattern);
        }

        public static bool IsValidSlaveId(string slaveId)
        {
            if (int.TryParse(slaveId, out int id))
            {
                return id >= 1 && id <= 247;
            }
            return false;
        }

        public static bool IsValidTimeout(int timeout)
        {
            return timeout > 0 && timeout <= 60000; // Max 60 segundos
        }

        public static bool IsValidRetryCount(int retryCount)
        {
            return retryCount >= 0 && retryCount <= 10;
        }

        public static bool IsValidFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return false;

            // Verificar caracteres inválidos para nombres de archivo
            var invalidChars = Path.GetInvalidFileNameChars();
            return fileName.IndexOfAny(invalidChars) == -1;
        }

        public static string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "unnamed";

            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = fileName;

            foreach (var c in invalidChars)
            {
                sanitized = sanitized.Replace(c, '_');
            }

            return sanitized;
        }
    }
}