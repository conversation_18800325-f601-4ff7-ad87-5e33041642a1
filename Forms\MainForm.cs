using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using ProFaceDataExtractor.Models;
using ProFaceDataExtractor.Services;
using Serilog;

namespace ProFaceDataExtractor.Forms
{
    public partial class MainForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ModbusCommunicationService _communicationService;
        private readonly CSVExportService _csvExportService;
        private readonly ConfigurationService _configurationService;
        private readonly ILogger _logger;

        private ApplicationSettings _settings;
        private System.Windows.Forms.Timer _readingTimer;
        private System.Windows.Forms.Timer _exportTimer;
        private Dictionary<string, Dictionary<string, Dictionary<string, object>>> _latestData;

        // Los controles UI ahora están declarados en MainForm.Designer.cs

        public MainForm(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _communicationService = serviceProvider.GetRequiredService<ModbusCommunicationService>();
            _csvExportService = serviceProvider.GetRequiredService<CSVExportService>();
            _configurationService = serviceProvider.GetRequiredService<ConfigurationService>();
            _logger = serviceProvider.GetRequiredService<ILogger>();

            _latestData = new Dictionary<string, Dictionary<string, Dictionary<string, object>>>();

            InitializeComponent();
            InitializeTimers();
            LoadSettingsAsync();
        }

        // InitializeComponent ahora está en MainForm.Designer.cs

        private void InitializeTimers()
        {
            _readingTimer = new System.Windows.Forms.Timer();
            _readingTimer.Tick += ReadingTimer_Tick;

            _exportTimer = new System.Windows.Forms.Timer();
            _exportTimer.Tick += ExportTimer_Tick;
        }

        private async void LoadSettingsAsync()
        {
            try
            {
                _settings = await _configurationService.LoadSettingsAsync();
                UpdateTreeView();

                _readingTimer.Interval = _settings.ReadingInterval;
                _exportTimer.Interval = _settings.AutoExportInterval;

                _csvExportService.SetExportPath(_settings.ExportPath);

                _logger.Information("Configuración cargada exitosamente");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error cargando configuración");
                MessageBox.Show($"Error cargando configuración: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateTreeView()
        {
            treeViewGateways.Nodes.Clear();

            foreach (var gateway in _settings.Gateways)
            {
                var gatewayNode = new TreeNode(gateway.Name)
                {
                    Tag = gateway,
                    ToolTipText = $"{gateway.IpAddress}:{gateway.Port}"
                };

                foreach (var plc in gateway.PLCs)
                {
                    var plcNode = new TreeNode(plc.Name)
                    {
                        Tag = plc,
                        ToolTipText = $"{plc.Brand} - Slave ID: {plc.SlaveId}"
                    };

                    foreach (var dataPoint in plc.DataPoints)
                    {
                        var dpNode = new TreeNode(dataPoint.Name)
                        {
                            Tag = dataPoint,
                            ToolTipText = $"Dirección: {dataPoint.Address}, Tipo: {dataPoint.DataType}"
                        };
                        plcNode.Nodes.Add(dpNode);
                    }
                    gatewayNode.Nodes.Add(plcNode);
                }
                treeViewGateways.Nodes.Add(gatewayNode);
            }

            treeViewGateways.ExpandAll();
        }

        private async void BtnStart_Click(object sender, EventArgs e)
        {
            try
            {
                statusLabel.Text = "Iniciando...";
                btnStart.Enabled = false;

                // Conectar a todos los gateways
                var connectionTasks = _settings.Gateways
                    .Where(g => g.IsEnabled)
                    .Select(g => _communicationService.ConnectToGatewayAsync(g));

                await Task.WhenAll(connectionTasks);

                _readingTimer.Start();
                if (_settings.AutoExportEnabled)
                    _exportTimer.Start();

                statusLabel.Text = "Ejecutándose";
                btnStart.Enabled = false;
                btnStop.Enabled = true;

                _logger.Information("Sistema iniciado exitosamente");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error iniciando sistema");
                MessageBox.Show($"Error iniciando sistema: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                btnStart.Enabled = true;
            }
        }

        private void BtnStop_Click(object sender, EventArgs e)
        {
            try
            {
                _readingTimer.Stop();
                _exportTimer.Stop();
                _communicationService.DisconnectAll();

                statusLabel.Text = "Detenido";
                btnStart.Enabled = true;
                btnStop.Enabled = false;

                _logger.Information("Sistema detenido");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error deteniendo sistema");
            }
        }

        private async void ReadingTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                _latestData.Clear();

                foreach (var gateway in _settings.Gateways.Where(g => g.IsEnabled))
                {
                    if (!_communicationService.IsConnected(gateway))
                        continue;

                    var gatewayData = new Dictionary<string, Dictionary<string, object>>();

                    foreach (var plc in gateway.PLCs.Where(p => p.IsEnabled))
                    {
                        var plcData = await _communicationService.ReadDataPointsAsync(gateway, plc);
                        if (plcData.Any())
                            gatewayData[plc.Name] = plcData;
                    }

                    if (gatewayData.Any())
                        _latestData[gateway.Name] = gatewayData;
                }

                UpdateDataGrid();
                updateLabel.Text = $"Última actualización: {DateTime.Now:HH:mm:ss}";
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error en ciclo de lectura");
            }
        }

        private void UpdateDataGrid()
        {
            // Implementar actualización del DataGridView con los datos leídos
            // Esta es una implementación básica
            dataGridViewData.Rows.Clear();
            dataGridViewData.Columns.Clear();

            if (!_latestData.Any()) return;

            // Crear columnas
            dataGridViewData.Columns.Add("Gateway", "Gateway");
            dataGridViewData.Columns.Add("PLC", "PLC");
            dataGridViewData.Columns.Add("Variable", "Variable");
            dataGridViewData.Columns.Add("Valor", "Valor");
            dataGridViewData.Columns.Add("Timestamp", "Timestamp");

            // Agregar filas
            foreach (var gateway in _latestData)
            {
                foreach (var plc in gateway.Value)
                {
                    foreach (var variable in plc.Value)
                    {
                        dataGridViewData.Rows.Add(
                            gateway.Key,
                            plc.Key,
                            variable.Key,
                            variable.Value,
                            DateTime.Now.ToString("HH:mm:ss")
                        );
                    }
                }
            }
        }

        private async void ExportTimer_Tick(object sender, EventArgs e)
        {
            if (_latestData.Any())
            {
                var filename = $"AutoExport_{DateTime.Now:yyyyMMdd_HH}.csv";
                await _csvExportService.AppendDataAsync(_latestData, filename);
            }
        }

        private async void BtnExportNow_Click(object sender, EventArgs e)
        {
            try
            {
                if (!_latestData.Any())
                {
                    MessageBox.Show("No hay datos para exportar", "Información", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                await _csvExportService.ExportDataAsync(_latestData);
                MessageBox.Show("Datos exportados exitosamente", "Éxito", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error exportando datos");
                MessageBox.Show($"Error exportando datos: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAddGateway_Click(object sender, EventArgs e)
        {
            var form = new GatewayConfigForm(_serviceProvider);
            if (form.ShowDialog() == DialogResult.OK)
            {
                _settings.Gateways.Add(form.Gateway);
                UpdateTreeView();
                _ = _configurationService.SaveSettingsAsync(_settings);
            }
        }

        private void BtnEditGateway_Click(object sender, EventArgs e)
        {
            var selectedNode = treeViewGateways.SelectedNode;
            if (selectedNode?.Tag is GatewayConfiguration gateway)
            {
                var form = new GatewayConfigForm(_serviceProvider, gateway);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    UpdateTreeView();
                    _ = _configurationService.SaveSettingsAsync(_settings);
                }
            }
        }

        private void BtnDeleteGateway_Click(object sender, EventArgs e)
        {
            var selectedNode = treeViewGateways.SelectedNode;
            if (selectedNode?.Tag is GatewayConfiguration gateway)
            {
                var result = MessageBox.Show($"¿Está seguro de eliminar el gateway '{gateway.Name}'?", 
                    "Confirmar eliminación", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _settings.Gateways.Remove(gateway);
                    UpdateTreeView();
                    _ = _configurationService.SaveSettingsAsync(_settings);
                }
            }
        }

        private void TreeViewGateways_AfterSelect(object sender, TreeViewEventArgs e)
        {
            btnEditGateway.Enabled = e.Node?.Tag is GatewayConfiguration;
            btnDeleteGateway.Enabled = e.Node?.Tag is GatewayConfiguration;
        }

        private void ExportConfig_Click(object sender, EventArgs e)
        {
            using var dialog = new SaveFileDialog
            {
                Filter = "JSON Files (*.json)|*.json",
                DefaultExt = "json"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                _ = _configurationService.ExportSettingsAsync(dialog.FileName, _settings);
            }
        }

        private async void ImportConfig_Click(object sender, EventArgs e)
        {
            using var dialog = new OpenFileDialog
            {
                Filter = "JSON Files (*.json)|*.json"
            };

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                var importedSettings = await _configurationService.ImportSettingsAsync(dialog.FileName);
                if (importedSettings != null)
                {
                    _settings = importedSettings;
                    UpdateTreeView();
                    MessageBox.Show("Configuración importada exitosamente", "Éxito", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void Settings_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Configuración general - Por implementar", "Información", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void About_Click(object sender, EventArgs e)
        {
            MessageBox.Show("ProFace Data Extractor v1.0\nDesarrollado para extracción de datos industriales",
                "Acerca de", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SalirToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            OnFormClosing(e);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                _readingTimer?.Stop();
                _exportTimer?.Stop();
                _communicationService?.DisconnectAll();
                _ = _configurationService?.SaveSettingsAsync(_settings);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error cerrando aplicación");
            }

            base.OnFormClosing(e);
        }
    }
}