# ProFace Data Extractor - Simple

Una aplicación simple y funcional para extraer datos de dispositivos ProFace GP5000 usando Modbus TCP/IP y almacenarlos en SQL Server.

## 🚀 Características

- **Conexión Modbus TCP/IP** usando EasyModbusTCP
- **Lectura de registros Holding** (40001, 40002, etc.)
- **Visualización en tiempo real** con DataGridView
- **Almacenamiento en SQL Server** con creación automática de tablas
- **Interfaz simple e intuitiva**
- **Manejo robusto de errores**

## 📋 Requisitos

- .NET 9.0 o superior
- Windows Forms
- SQL Server (opcional, para almacenamiento)
- Dispositivo ProFace GP5000 con Modbus TCP/IP habilitado

## 🔧 Instalación

1. Clonar o descargar el proyecto
2. Abrir en Visual Studio o VS Code
3. Restaurar paquetes NuGet:
   ```bash
   dotnet restore
   ```
4. Compilar:
   ```bash
   dotnet build
   ```
5. Ejecutar:
   ```bash
   dotnet run
   ```

## 📖 Uso

### 1. Conexión Modbus
- Introducir la **IP** del dispositivo ProFace (ej: *************)
- Introducir el **Puerto** Modbus (por defecto: 502)
- Hacer clic en **"Conectar"**

### 2. Lectura de Datos
- Especificar la **dirección** del registro (ej: 40001)
- Hacer clic en **"Leer"**
- Los datos aparecerán en la tabla con timestamp

### 3. Guardado en Base de Datos
- Configurar la **cadena de conexión** a SQL Server
- Hacer clic en **"Guardar"** para almacenar todos los datos
- La tabla `ModbusData` se crea automáticamente

## 🗄️ Estructura de Base de Datos

```sql
CREATE TABLE ModbusData (
    Id int IDENTITY(1,1) PRIMARY KEY,
    Timestamp datetime NOT NULL,
    Address nvarchar(50) NOT NULL,
    Value int NOT NULL,
    Description nvarchar(200)
)
```

## 🔧 Configuración

### Direcciones Modbus Soportadas
- **40001-49999**: Holding Registers (se convierte automáticamente a 0-8998)
- **30001-39999**: Input Registers (se convierte automáticamente a 0-8998)
- **Direcciones directas**: 0, 1, 2, etc.

### Cadena de Conexión SQL Server
```
Server=localhost;Database=ProFaceData;Integrated Security=true;
```

## 🛠️ Tecnologías Utilizadas

- **.NET 9.0** - Framework principal
- **Windows Forms** - Interfaz gráfica
- **EasyModbusTCP 5.6.0** - Comunicación Modbus
- **Microsoft.Data.SqlClient 6.0.2** - Conexión SQL Server

## 📁 Estructura del Proyecto

```
ProFaceSimple/
├── Form1.cs              # Lógica principal
├── Form1.Designer.cs     # Diseño del formulario
├── Program.cs            # Punto de entrada
├── ProFaceSimple.csproj  # Configuración del proyecto
└── README.md             # Este archivo
```

## 🎯 Funcionalidades Principales

✅ **Conexión/Desconexión** automática con indicadores visuales
✅ **Lectura de registros** con parseo automático de direcciones
✅ **Visualización de datos** con timestamp y descripción
✅ **Almacenamiento persistente** en SQL Server
✅ **Manejo de errores** con mensajes informativos
✅ **Estado de conexión** en barra de estado

## 🚨 Solución de Problemas

### Error de Conexión Modbus
- Verificar que el dispositivo esté encendido y conectado a la red
- Comprobar la IP y puerto
- Verificar que Modbus TCP/IP esté habilitado en el dispositivo

### Error de Base de Datos
- Verificar que SQL Server esté ejecutándose
- Comprobar la cadena de conexión
- Verificar permisos de escritura en la base de datos

## 📝 Licencia

Este proyecto es de código abierto y está disponible bajo la licencia MIT.

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor, crear un issue o pull request para mejoras.

---

**¡Aplicación lista para usar en producción!** 🎉
