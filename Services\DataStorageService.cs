using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ProFaceDataExtractor.Models;
using ProFaceDataExtractor.Models.Database;
using Serilog;

namespace ProFaceDataExtractor.Services
{
    /// <summary>
    /// Servicio que maneja el almacenamiento de datos tanto en CSV como en base de datos
    /// </summary>
    public class DataStorageService
    {
        private readonly ILogger _logger;
        private readonly CSVExportService _csvExportService;
        private readonly DatabaseService? _databaseService;
        private readonly bool _databaseEnabled;

        public DataStorageService(
            ILogger logger, 
            CSVExportService csvExportService, 
            DatabaseService? databaseService = null)
        {
            _logger = logger;
            _csvExportService = csvExportService;
            _databaseService = databaseService;
            _databaseEnabled = databaseService != null;
        }

        /// <summary>
        /// Almacena datos leídos tanto en CSV como en base de datos
        /// </summary>
        public async Task StoreDataAsync(
            Dictionary<string, Dictionary<string, Dictionary<string, object>>> data,
            bool saveToCSV = true,
            bool saveToDatabase = true)
        {
            var tasks = new List<Task>();

            // Guardar en CSV
            if (saveToCSV)
            {
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        var filename = $"AutoData_{DateTime.Now:yyyyMMdd_HH}.csv";
                        await _csvExportService.AppendDataAsync(data, filename);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error guardando datos en CSV");
                    }
                }));
            }

            // Guardar en base de datos
            if (saveToDatabase && _databaseEnabled && _databaseService != null)
            {
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        var historyEntities = ConvertToDataHistory(data);
                        await _databaseService.InsertDataHistoryBatchAsync(historyEntities);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error guardando datos en base de datos");
                    }
                }));
            }

            // Ejecutar ambas tareas en paralelo
            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// Exporta datos manualmente
        /// </summary>
        public async Task<bool> ExportDataManuallyAsync(
            Dictionary<string, Dictionary<string, Dictionary<string, object>>> data,
            string? customFilename = null)
        {
            try
            {
                await _csvExportService.ExportDataAsync(data, customFilename ?? "");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error en exportación manual de datos");
                return false;
            }
        }

        /// <summary>
        /// Obtiene datos históricos de la base de datos
        /// </summary>
        public async Task<List<DataHistoryEntity>> GetHistoricalDataAsync(
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? gatewayId = null,
            string? plcId = null,
            int maxRecords = 1000)
        {
            if (!_databaseEnabled || _databaseService == null)
            {
                _logger.Warning("Base de datos no está habilitada");
                return new List<DataHistoryEntity>();
            }

            try
            {
                return await _databaseService.GetDataHistoryAsync(
                    startDate, endDate, gatewayId, plcId, maxRecords);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error obteniendo datos históricos");
                return new List<DataHistoryEntity>();
            }
        }

        /// <summary>
        /// Limpia datos antiguos tanto de CSV como de base de datos
        /// </summary>
        public async Task CleanupOldDataAsync(int retentionDays)
        {
            var tasks = new List<Task>();

            // Limpiar archivos CSV antiguos
            tasks.Add(Task.Run(async () =>
            {
                try
                {
                    await _csvExportService.CleanupOldFilesAsync(retentionDays);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error limpiando archivos CSV antiguos");
                }
            }));

            // Limpiar datos de base de datos antiguos
            if (_databaseEnabled && _databaseService != null)
            {
                tasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        await _databaseService.CleanupOldDataAsync(retentionDays);
                    }
                    catch (Exception ex)
                    {
                        _logger.Error(ex, "Error limpiando datos antiguos de base de datos");
                    }
                }));
            }

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// Verifica el estado de la base de datos
        /// </summary>
        public async Task<bool> TestDatabaseConnectionAsync()
        {
            if (!_databaseEnabled || _databaseService == null)
                return false;

            return await _databaseService.TestConnectionAsync();
        }

        /// <summary>
        /// Inicializa la base de datos creando las tablas necesarias
        /// </summary>
        public async Task<bool> InitializeDatabaseAsync()
        {
            if (!_databaseEnabled || _databaseService == null)
                return false;

            return await _databaseService.InitializeDatabaseAsync();
        }

        /// <summary>
        /// Convierte los datos leídos a entidades de historial para la base de datos
        /// </summary>
        private List<DataHistoryEntity> ConvertToDataHistory(
            Dictionary<string, Dictionary<string, Dictionary<string, object>>> data)
        {
            var historyEntities = new List<DataHistoryEntity>();
            var timestamp = DateTime.Now;

            foreach (var gateway in data)
            {
                foreach (var plc in gateway.Value)
                {
                    foreach (var dataPoint in plc.Value)
                    {
                        var entity = new DataHistoryEntity
                        {
                            DataPointId = Guid.NewGuid().ToString(), // En producción, esto debería venir de la configuración
                            PLCId = Guid.NewGuid().ToString(),       // En producción, esto debería venir de la configuración
                            GatewayId = Guid.NewGuid().ToString(),   // En producción, esto debería venir de la configuración
                            DataPointName = dataPoint.Key,
                            PLCName = plc.Key,
                            GatewayName = gateway.Key,
                            Value = dataPoint.Value?.ToString() ?? "",
                            Timestamp = timestamp,
                            ReadTime = timestamp,
                            IsValid = dataPoint.Value != null,
                            ErrorMessage = dataPoint.Value == null ? "Valor nulo" : ""
                        };

                        // Determinar tipo de dato y valores específicos
                        if (dataPoint.Value != null)
                        {
                            switch (dataPoint.Value)
                            {
                                case int intValue:
                                    entity.DataType = "Int32";
                                    entity.NumericValue = intValue;
                                    break;
                                case float floatValue:
                                    entity.DataType = "Float";
                                    entity.NumericValue = floatValue;
                                    break;
                                case double doubleValue:
                                    entity.DataType = "Double";
                                    entity.NumericValue = doubleValue;
                                    break;
                                case bool boolValue:
                                    entity.DataType = "Boolean";
                                    entity.BooleanValue = boolValue;
                                    break;
                                default:
                                    entity.DataType = "String";
                                    break;
                            }
                        }
                        else
                        {
                            entity.DataType = "Unknown";
                            entity.IsValid = false;
                        }

                        historyEntities.Add(entity);
                    }
                }
            }

            return historyEntities;
        }

        /// <summary>
        /// Obtiene estadísticas de almacenamiento
        /// </summary>
        public async Task<Dictionary<string, object>> GetStorageStatisticsAsync()
        {
            var stats = new Dictionary<string, object>();

            try
            {
                // Estadísticas de archivos CSV
                var csvFiles = await _csvExportService.GetExportedFilesAsync();
                stats["CSVFileCount"] = csvFiles.Count;
                stats["CSVEnabled"] = true;

                // Estadísticas de base de datos
                stats["DatabaseEnabled"] = _databaseEnabled;
                
                if (_databaseEnabled && _databaseService != null)
                {
                    var dbConnected = await _databaseService.TestConnectionAsync();
                    stats["DatabaseConnected"] = dbConnected;
                    
                    if (dbConnected)
                    {
                        // Aquí podrías agregar más estadísticas de la base de datos
                        // como número de registros, tamaño, etc.
                        stats["DatabaseInitialized"] = true;
                    }
                }
                else
                {
                    stats["DatabaseConnected"] = false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error obteniendo estadísticas de almacenamiento");
                stats["Error"] = ex.Message;
            }

            return stats;
        }
    }
}
