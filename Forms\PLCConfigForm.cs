using System;
using System.Drawing;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using ProFaceDataExtractor.Models;
using ProFaceDataExtractor.Utils;
using Serilog;

namespace ProFaceDataExtractor.Forms
{
    public partial class PLCConfigForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger _logger;
        public PLCConfiguration PLC { get; private set; }

        // Controles UI
        private TextBox txtName, txtModel, txtSlaveId, txtDescription;
        private ComboBox comboBrand;
        private CheckBox chkEnabled;
        private Button btnOK, btnCancel;

        public PLCConfigForm(IServiceProvider serviceProvider, PLCConfiguration? plc = null)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetRequiredService<ILogger>();

            PLC = plc ?? new PLCConfiguration();

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = PLC.Id == Guid.Empty.ToString() ? "Nuevo PLC" : "Editar PLC";
            this.Size = new Size(400, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var y = 20;
            var labelWidth = 100;
            var textWidth = 200;
            var spacing = 35;

            // Nombre
            var lblName = new Label { Text = "Nombre:", Location = new Point(20, y), Size = new Size(labelWidth, 20) };
            txtName = new TextBox { Location = new Point(130, y), Size = new Size(textWidth, 20) };
            y += spacing;

            // Marca
            var lblBrand = new Label { Text = "Marca:", Location = new Point(20, y), Size = new Size(labelWidth, 20) };
            comboBrand = new ComboBox { Location = new Point(130, y), Size = new Size(textWidth, 20), DropDownStyle = ComboBoxStyle.DropDownList };
            comboBrand.DataSource = Enum.GetValues(typeof(PLCBrand));
            y += spacing;

            // Modelo
            var lblModel = new Label { Text = "Modelo:", Location = new Point(20, y), Size = new Size(labelWidth, 20) };
            txtModel = new TextBox { Location = new Point(130, y), Size = new Size(textWidth, 20) };
            y += spacing;

            // Slave ID
            var lblSlaveId = new Label { Text = "Slave ID:", Location = new Point(20, y), Size = new Size(labelWidth, 20) };
            txtSlaveId = new TextBox { Location = new Point(130, y), Size = new Size(100, 20) };
            y += spacing;

            // Habilitado
            chkEnabled = new CheckBox { Text = "Habilitado", Location = new Point(130, y), Size = new Size(100, 20) };
            y += spacing;

            // Descripción
            var lblDescription = new Label { Text = "Descripción:", Location = new Point(20, y), Size = new Size(labelWidth, 20) };
            txtDescription = new TextBox { Location = new Point(130, y), Size = new Size(textWidth, 60), Multiline = true, ScrollBars = ScrollBars.Vertical };
            y += 80;

            // Botones
            btnOK = new Button { Text = "Aceptar", Size = new Size(80, 30), Location = new Point(200, 300), DialogResult = DialogResult.OK };
            btnOK.Click += BtnOK_Click;
            btnCancel = new Button { Text = "Cancelar", Size = new Size(80, 30), Location = new Point(290, 300), DialogResult = DialogResult.Cancel };

            this.Controls.AddRange(new Control[] { 
                lblName, txtName,
                lblBrand, comboBrand,
                lblModel, txtModel,
                lblSlaveId, txtSlaveId,
                chkEnabled,
                lblDescription, txtDescription,
                btnOK, btnCancel
            });

            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
        }

        private void LoadData()
        {
            txtName.Text = PLC.Name;
            comboBrand.SelectedItem = PLC.Brand;
            txtModel.Text = PLC.Model;
            txtSlaveId.Text = PLC.SlaveId;
            txtDescription.Text = PLC.Description;
            chkEnabled.Checked = PLC.IsEnabled;
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                SaveData();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error guardando PLC");
                MessageBox.Show($"Error guardando PLC: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("El nombre es requerido", "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (!ValidationHelper.IsValidSlaveId(txtSlaveId.Text))
            {
                MessageBox.Show("El Slave ID debe ser un número entre 1 y 247", "Validación", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtSlaveId.Focus();
                return false;
            }

            return true;
        }

        private void SaveData()
        {
            PLC.Name = txtName.Text.Trim();
            PLC.Brand = (PLCBrand)comboBrand.SelectedItem;
            PLC.Model = txtModel.Text.Trim();
            PLC.SlaveId = txtSlaveId.Text.Trim();
            PLC.Description = txtDescription.Text.Trim();
            PLC.IsEnabled = chkEnabled.Checked;
            PLC.LastModified = DateTime.Now;
        }
    }
}