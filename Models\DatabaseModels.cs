using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ProFaceDataExtractor.Models.Database
{
    /// <summary>
    /// Tabla para almacenar configuraciones de gateways
    /// </summary>
    [Table("Gateways")]
    public class GatewayEntity
    {
        [Key]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(15)]
        public string IpAddress { get; set; } = string.Empty;
        
        public int Port { get; set; }
        public int Timeout { get; set; }
        public int RetryCount { get; set; }
        public bool IsEnabled { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Tabla para almacenar configuraciones de PLCs
    /// </summary>
    [Table("PLCs")]
    public class PLCEntity
    {
        [Key]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public string GatewayId { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string Brand { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Model { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(10)]
        public string SlaveId { get; set; } = string.Empty;
        
        public bool IsEnabled { get; set; }
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        
        [ForeignKey("GatewayId")]
        public virtual GatewayEntity Gateway { get; set; } = null!;
    }

    /// <summary>
    /// Tabla para almacenar configuraciones de puntos de datos
    /// </summary>
    [Table("DataPoints")]
    public class DataPointEntity
    {
        [Key]
        public string Id { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        public string PLCId { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(20)]
        public string Address { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(20)]
        public string DataType { get; set; } = string.Empty;
        
        public bool IsEnabled { get; set; }
        public double ScaleFactor { get; set; } = 1.0;
        public double Offset { get; set; } = 0.0;
        
        [MaxLength(20)]
        public string Unit { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        
        [ForeignKey("PLCId")]
        public virtual PLCEntity PLC { get; set; } = null!;
    }

    /// <summary>
    /// Tabla para almacenar datos históricos leídos
    /// </summary>
    [Table("DataHistory")]
    public class DataHistoryEntity
    {
        [Key]
        public long Id { get; set; }
        
        [Required]
        public string DataPointId { get; set; } = string.Empty;
        
        [Required]
        public string PLCId { get; set; } = string.Empty;
        
        [Required]
        public string GatewayId { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string DataPointName { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string PLCName { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string GatewayName { get; set; } = string.Empty;
        
        public string Value { get; set; } = string.Empty;
        
        [MaxLength(20)]
        public string DataType { get; set; } = string.Empty;
        
        public double? NumericValue { get; set; }
        public bool? BooleanValue { get; set; }
        
        [MaxLength(20)]
        public string Unit { get; set; } = string.Empty;
        
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        public DateTime ReadTime { get; set; } = DateTime.Now;
        
        public bool IsValid { get; set; } = true;
        
        [MaxLength(500)]
        public string ErrorMessage { get; set; } = string.Empty;
        
        [ForeignKey("DataPointId")]
        public virtual DataPointEntity DataPoint { get; set; } = null!;
        
        [ForeignKey("PLCId")]
        public virtual PLCEntity PLC { get; set; } = null!;
        
        [ForeignKey("GatewayId")]
        public virtual GatewayEntity Gateway { get; set; } = null!;
    }

    /// <summary>
    /// Tabla para almacenar configuraciones de la aplicación
    /// </summary>
    [Table("ApplicationConfig")]
    public class ApplicationConfigEntity
    {
        [Key]
        [MaxLength(100)]
        public string Key { get; set; } = string.Empty;
        
        public string Value { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Tabla para almacenar logs de eventos
    /// </summary>
    [Table("EventLogs")]
    public class EventLogEntity
    {
        [Key]
        public long Id { get; set; }
        
        [Required]
        [MaxLength(20)]
        public string Level { get; set; } = string.Empty;
        
        [Required]
        public string Message { get; set; } = string.Empty;
        
        public string Exception { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Source { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string Category { get; set; } = string.Empty;
        
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        [MaxLength(50)]
        public string MachineName { get; set; } = Environment.MachineName;
        
        [MaxLength(50)]
        public string UserName { get; set; } = Environment.UserName;
    }
}
