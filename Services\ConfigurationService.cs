using System;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;
using ProFaceDataExtractor.Models;
using Serilog;

namespace ProFaceDataExtractor.Services
{
    public class ConfigurationService
    {
        private readonly ILogger _logger;
        private readonly string _configPath;
        private readonly string _backupPath;

        public ConfigurationService(ILogger logger)
        {
            _logger = logger;
            _configPath = "./Config";
            _backupPath = "./Config/Backups";

            Directory.CreateDirectory(_configPath);
            Directory.CreateDirectory(_backupPath);
        }

        public async Task<ApplicationSettings> LoadSettingsAsync()
        {
            try
            {
                var settingsFile = Path.Combine(_configPath, "settings.json");

                if (!File.Exists(settingsFile))
                {
                    _logger.Information("Archivo de configuración no encontrado, creando configuración por defecto");
                    var defaultSettings = new ApplicationSettings();
                    await SaveSettingsAsync(defaultSettings);
                    return defaultSettings;
                }

                var json = await File.ReadAllTextAsync(settingsFile);
                var settings = JsonConvert.DeserializeObject<ApplicationSettings>(json);

                if (settings == null)
                {
                    _logger.Warning("Error deserializando configuración, usando configuración por defecto");
                    return new ApplicationSettings();
                }

                _logger.Information($"Configuración cargada exitosamente. {settings.Gateways.Count} gateways configurados");
                return settings;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error cargando configuración: {ex.Message}");
                return new ApplicationSettings();
            }
        }

        public async Task SaveSettingsAsync(ApplicationSettings settings)
        {
            try
            {
                // Crear backup antes de guardar
                await CreateBackupAsync();

                settings.LastModified = DateTime.Now;
                var json = JsonConvert.SerializeObject(settings, Formatting.Indented);
                var settingsFile = Path.Combine(_configPath, "settings.json");

                await File.WriteAllTextAsync(settingsFile, json);
                _logger.Information("Configuración guardada exitosamente");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error guardando configuración: {ex.Message}");
            }
        }

        public async Task ExportSettingsAsync(string filePath, ApplicationSettings settings)
        {
            try
            {
                var json = JsonConvert.SerializeObject(settings, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                _logger.Information($"Configuración exportada a: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error exportando configuración: {ex.Message}");
            }
        }

        public async Task<ApplicationSettings?> ImportSettingsAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.Warning($"Archivo de configuración no encontrado: {filePath}");
                    return null;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var settings = JsonConvert.DeserializeObject<ApplicationSettings>(json);

                if (settings == null)
                {
                    _logger.Warning("Error deserializando configuración importada");
                    return null;
                }

                _logger.Information($"Configuración importada exitosamente desde: {filePath}");
                return settings;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error importando configuración: {ex.Message}");
                return null;
            }
        }

        private async Task CreateBackupAsync()
        {
            try
            {
                var settingsFile = Path.Combine(_configPath, "settings.json");
                if (!File.Exists(settingsFile))
                    return;

                var backupFileName = $"settings_backup_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var backupFilePath = Path.Combine(_backupPath, backupFileName);

                File.Copy(settingsFile, backupFilePath);

                // Limpiar backups antiguos (mantener solo los últimos 10)
                await CleanupOldBackupsAsync();
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, $"Error creando backup: {ex.Message}");
            }
        }

        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupPath, "settings_backup_*.json");

                if (backupFiles.Length <= 10)
                    return;

                Array.Sort(backupFiles);

                for (int i = 0; i < backupFiles.Length - 10; i++)
                {
                    File.Delete(backupFiles[i]);
                }
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, $"Error limpiando backups antiguos: {ex.Message}");
            }
        }

        public async Task<bool> ValidateConfigurationAsync(ApplicationSettings settings)
        {
            try
            {
                // Validaciones básicas
                if (settings.Gateways == null)
                {
                    _logger.Warning("Lista de gateways es null");
                    return false;
                }

                foreach (var gateway in settings.Gateways)
                {
                    if (string.IsNullOrEmpty(gateway.IpAddress))
                    {
                        _logger.Warning($"Gateway {gateway.Name} no tiene dirección IP configurada");
                        return false;
                    }

                    if (gateway.Port <= 0 || gateway.Port > 65535)
                    {
                        _logger.Warning($"Gateway {gateway.Name} tiene puerto inválido: {gateway.Port}");
                        return false;
                    }

                    foreach (var plc in gateway.PLCs)
                    {
                        if (string.IsNullOrEmpty(plc.SlaveId))
                        {
                            _logger.Warning($"PLC {plc.Name} no tiene Slave ID configurado");
                            return false;
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error validando configuración: {ex.Message}");
                return false;
            }
        }
    }
}