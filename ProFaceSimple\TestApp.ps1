# Script para probar la aplicación ProFace Simple
Write-Host "🚀 Probando ProFace Data Extractor - Simple" -ForegroundColor Green

# Verificar que la aplicación está corriendo
$processes = Get-Process | Where-Object { $_.ProcessName -like "*ProFaceSimple*" }
if ($processes.Count -gt 0) {
    Write-Host "✅ Aplicación está ejecutándose" -ForegroundColor Green
    Write-Host "   Procesos encontrados: $($processes.Count)" -ForegroundColor Yellow
    
    foreach ($proc in $processes) {
        Write-Host "   - PID: $($proc.Id), Nombre: $($proc.ProcessName)" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ Aplicación no está ejecutándose" -ForegroundColor Red
}

# Verificar archivos compilados
$exePath = ".\bin\Debug\net9.0-windows\ProFaceSimple.exe"
if (Test-Path $exePath) {
    Write-Host "✅ Ejecutable compilado correctamente" -ForegroundColor Green
    $fileInfo = Get-Item $exePath
    Write-Host "   Tamaño: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Cyan
    Write-Host "   Fecha: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
} else {
    Write-Host "❌ Ejecutable no encontrado" -ForegroundColor Red
}

# Verificar dependencias
Write-Host "`n📦 Verificando dependencias:" -ForegroundColor Yellow
$packagesPath = ".\obj\project.assets.json"
if (Test-Path $packagesPath) {
    $assets = Get-Content $packagesPath | ConvertFrom-Json
    $packages = $assets.libraries.PSObject.Properties.Name
    
    Write-Host "   Paquetes instalados:" -ForegroundColor Cyan
    foreach ($package in $packages) {
        if ($package -like "*EasyModbus*" -or $package -like "*SqlClient*") {
            Write-Host "   ✅ $package" -ForegroundColor Green
        }
    }
}

Write-Host "`n🎯 Funcionalidades implementadas:" -ForegroundColor Yellow
Write-Host "   ✅ Interfaz gráfica con Windows Forms" -ForegroundColor Green
Write-Host "   ✅ Conexión Modbus TCP/IP con EasyModbus" -ForegroundColor Green
Write-Host "   ✅ Lectura de registros Holding" -ForegroundColor Green
Write-Host "   ✅ Visualización de datos en DataGridView" -ForegroundColor Green
Write-Host "   ✅ Guardado en base de datos SQL Server" -ForegroundColor Green
Write-Host "   ✅ Manejo de errores y estado de conexión" -ForegroundColor Green

Write-Host "`n📋 Para probar la aplicación:" -ForegroundColor Yellow
Write-Host "   1. La aplicación debería estar visible en pantalla" -ForegroundColor Cyan
Write-Host "   2. Configurar IP y puerto del dispositivo Modbus" -ForegroundColor Cyan
Write-Host "   3. Hacer clic en 'Conectar'" -ForegroundColor Cyan
Write-Host "   4. Especificar dirección de registro (ej: 40001)" -ForegroundColor Cyan
Write-Host "   5. Hacer clic en 'Leer' para obtener datos" -ForegroundColor Cyan
Write-Host "   6. Configurar cadena de conexión a SQL Server" -ForegroundColor Cyan
Write-Host "   7. Hacer clic en 'Guardar' para almacenar en BD" -ForegroundColor Cyan

Write-Host "`n🎉 Aplicación lista para usar!" -ForegroundColor Green
