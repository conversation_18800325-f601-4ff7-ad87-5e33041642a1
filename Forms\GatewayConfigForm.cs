using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Extensions.DependencyInjection;
using ProFaceDataExtractor.Models;
using ProFaceDataExtractor.Utils;
using Serilog;

namespace ProFaceDataExtractor.Forms
{
    public partial class GatewayConfigForm : Form
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger _logger;
        public GatewayConfiguration Gateway { get; private set; }

        // Controles UI
        private TabControl tabControl;
        private TabPage tabGeneral, tabPLCs;

        // Tab General
        private TextBox txtName, txtIpAddress, txtPort, txtTimeout, txtRetryCount, txtDescription;
        private CheckBox chkEnabled;
        private Button btnTestConnection;

        // Tab PLCs
        private ListBox listPLCs;
        private Button btnAddPLC, btnEditPLC, btnDeletePLC;

        // Botones principales
        private Button btnOK, btnCancel;

        public GatewayConfigForm(IServiceProvider serviceProvider, GatewayConfiguration? gateway = null)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetRequiredService<ILogger>();

            Gateway = gateway ?? new GatewayConfiguration();

            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.Text = Gateway.Id == Guid.Empty.ToString() ? "Nuevo Gateway" : "Editar Gateway";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Tab Control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Margin = new Padding(10)
            };

            // Tab General
            tabGeneral = new TabPage("General");
            InitializeGeneralTab();

            // Tab PLCs
            tabPLCs = new TabPage("PLCs");
            InitializePLCsTab();

            tabControl.TabPages.AddRange(new TabPage[] { tabGeneral, tabPLCs });

            // Botones principales
            var buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom
            };

            btnOK = new Button
            {
                Text = "Aceptar",
                Size = new Size(80, 30),
                Location = new Point(430, 10),
                DialogResult = DialogResult.OK
            };
            btnOK.Click += BtnOK_Click;

            btnCancel = new Button
            {
                Text = "Cancelar",
                Size = new Size(80, 30),  
                Location = new Point(520, 10),
                DialogResult = DialogResult.Cancel
            };

            buttonPanel.Controls.AddRange(new Control[] { btnOK, btnCancel });

            this.Controls.Add(tabControl);
            this.Controls.Add(buttonPanel);

            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
        }

        private void InitializeGeneralTab()
        {
            var y = 20;
            var labelWidth = 120;
            var textWidth = 200;
            var spacing = 35;

            // Nombre
            var lblName = new Label
            {
                Text = "Nombre:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtName = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(textWidth, 20)
            };

            y += spacing;

            // Dirección IP
            var lblIpAddress = new Label
            {
                Text = "Dirección IP:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtIpAddress = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(textWidth, 20)
            };

            y += spacing;

            // Puerto
            var lblPort = new Label
            {
                Text = "Puerto:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtPort = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(100, 20)
            };

            btnTestConnection = new Button
            {
                Text = "Probar Conexión",
                Location = new Point(260, y),
                Size = new Size(120, 25)
            };
            btnTestConnection.Click += BtnTestConnection_Click;

            y += spacing;

            // Timeout
            var lblTimeout = new Label
            {
                Text = "Timeout (ms):",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtTimeout = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(100, 20)
            };

            y += spacing;

            // Reintentos
            var lblRetryCount = new Label
            {
                Text = "Reintentos:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtRetryCount = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(100, 20)
            };

            y += spacing;

            // Habilitado
            chkEnabled = new CheckBox
            {
                Text = "Habilitado",
                Location = new Point(150, y),
                Size = new Size(100, 20)
            };

            y += spacing;

            // Descripción
            var lblDescription = new Label
            {
                Text = "Descripción:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtDescription = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(textWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            tabGeneral.Controls.AddRange(new Control[]
            {
                lblName, txtName,
                lblIpAddress, txtIpAddress,
                lblPort, txtPort, btnTestConnection,
                lblTimeout, txtTimeout,
                lblRetryCount, txtRetryCount,
                chkEnabled,
                lblDescription, txtDescription
            });
        }

        private void InitializePLCsTab()
        {
            // Lista de PLCs
            listPLCs = new ListBox
            {
                Location = new Point(20, 20),
                Size = new Size(400, 300),
                DisplayMember = "Name"
            };
            listPLCs.SelectedIndexChanged += ListPLCs_SelectedIndexChanged;

            // Botones para PLCs
            btnAddPLC = new Button
            {
                Text = "Agregar PLC",
                Location = new Point(440, 20),
                Size = new Size(100, 30)
            };
            btnAddPLC.Click += BtnAddPLC_Click;

            btnEditPLC = new Button
            {
                Text = "Editar PLC",
                Location = new Point(440, 60),
                Size = new Size(100, 30),
                Enabled = false
            };
            btnEditPLC.Click += BtnEditPLC_Click;

            btnDeletePLC = new Button
            {
                Text = "Eliminar PLC",
                Location = new Point(440, 100),
                Size = new Size(100, 30),
                Enabled = false
            };
            btnDeletePLC.Click += BtnDeletePLC_Click;

            tabPLCs.Controls.AddRange(new Control[]
            {
                listPLCs, btnAddPLC, btnEditPLC, btnDeletePLC
            });
        }

        private void LoadData()
        {
            txtName.Text = Gateway.Name;
            txtIpAddress.Text = Gateway.IpAddress;
            txtPort.Text = Gateway.Port.ToString();
            txtTimeout.Text = Gateway.Timeout.ToString();
            txtRetryCount.Text = Gateway.RetryCount.ToString();
            txtDescription.Text = Gateway.Description;
            chkEnabled.Checked = Gateway.IsEnabled;

            RefreshPLCList();
        }

        private void RefreshPLCList()
        {
            listPLCs.Items.Clear();
            foreach (var plc in Gateway.PLCs)
            {
                listPLCs.Items.Add(plc);
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                SaveData();
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error guardando configuración de Gateway");
                MessageBox.Show($"Error guardando configuración: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateForm()
        {
            // Validar nombre
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("El nombre es requerido", "Validación", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // Validar IP
            if (!ValidationHelper.IsValidIpAddress(txtIpAddress.Text))
            {
                MessageBox.Show("La dirección IP no es válida", "Validación", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtIpAddress.Focus();
                return false;
            }

            // Validar puerto
            if (!int.TryParse(txtPort.Text, out int port) || !ValidationHelper.IsValidPort(port))
            {
                MessageBox.Show("El puerto debe ser un número entre 1 y 65535", "Validación", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPort.Focus();
                return false;
            }

            // Validar timeout
            if (!int.TryParse(txtTimeout.Text, out int timeout) || !ValidationHelper.IsValidTimeout(timeout))
            {
                MessageBox.Show("El timeout debe ser un número entre 1 y 60000", "Validación", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtTimeout.Focus();
                return false;
            }

            // Validar reintentos
            if (!int.TryParse(txtRetryCount.Text, out int retryCount) || !ValidationHelper.IsValidRetryCount(retryCount))
            {
                MessageBox.Show("Los reintentos deben ser un número entre 0 y 10", "Validación", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRetryCount.Focus();
                return false;
            }

            return true;
        }

        private void SaveData()
        {
            Gateway.Name = txtName.Text.Trim();
            Gateway.IpAddress = txtIpAddress.Text.Trim();
            Gateway.Port = int.Parse(txtPort.Text);
            Gateway.Timeout = int.Parse(txtTimeout.Text);
            Gateway.RetryCount = int.Parse(txtRetryCount.Text);
            Gateway.Description = txtDescription.Text.Trim();
            Gateway.IsEnabled = chkEnabled.Checked;
            Gateway.LastModified = DateTime.Now;
        }

        private async void BtnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidationHelper.IsValidIpAddress(txtIpAddress.Text))
                {
                    MessageBox.Show("Ingrese una dirección IP válida", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!int.TryParse(txtPort.Text, out int port) || !ValidationHelper.IsValidPort(port))
                {
                    MessageBox.Show("Ingrese un puerto válido", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                btnTestConnection.Enabled = false;
                btnTestConnection.Text = "Probando...";

                var tempGateway = new GatewayConfiguration
                {
                    IpAddress = txtIpAddress.Text.Trim(),
                    Port = port,
                    Timeout = int.TryParse(txtTimeout.Text, out int timeout) ? timeout : 5000
                };

                var communicationService = _serviceProvider.GetRequiredService<ModbusCommunicationService>();
                var result = await communicationService.ConnectToGatewayAsync(tempGateway);

                if (result)
                {
                    MessageBox.Show("Conexión exitosa", "Prueba de Conexión", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("No se pudo establecer conexión", "Prueba de Conexión", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error probando conexión");
                MessageBox.Show($"Error probando conexión: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnTestConnection.Enabled = true;
                btnTestConnection.Text = "Probar Conexión";
            }
        }

        private void BtnAddPLC_Click(object sender, EventArgs e)
        {
            var form = new PLCConfigForm(_serviceProvider);
            if (form.ShowDialog() == DialogResult.OK)
            {
                Gateway.PLCs.Add(form.PLC);
                RefreshPLCList();
            }
        }

        private void BtnEditPLC_Click(object sender, EventArgs e)
        {
            if (listPLCs.SelectedItem is PLCConfiguration selectedPLC)
            {
                var form = new PLCConfigForm(_serviceProvider, selectedPLC);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    RefreshPLCList();
                }
            }
        }

        private void BtnDeletePLC_Click(object sender, EventArgs e)
        {
            if (listPLCs.SelectedItem is PLCConfiguration selectedPLC)
            {
                var result = MessageBox.Show($"¿Está seguro de eliminar el PLC '{selectedPLC.Name}'?", 
                    "Confirmar eliminación", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    Gateway.PLCs.Remove(selectedPLC);
                    RefreshPLCList();
                }
            }
        }

        private void ListPLCs_SelectedIndexChanged(object sender, EventArgs e)
        {
            var hasSelection = listPLCs.SelectedItem != null;
            btnEditPLC.Enabled = hasSelection;
            btnDeletePLC.Enabled = hasSelection;
        }
    }
}