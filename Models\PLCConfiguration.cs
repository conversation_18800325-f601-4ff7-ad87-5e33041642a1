using System;
using System.Collections.Generic;

namespace ProFaceDataExtractor.Models
{
    public enum PLCBrand
    {
        Siemens,
        AllenBradley,
        SchneiderElectric,
        Mitsubishi,
        Omron,
        ABB,
        Other
    }

    public class PLCConfiguration
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public PLCBrand Brand { get; set; } = PLCBrand.Siemens;
        public string Model { get; set; } = string.Empty;
        public string SlaveId { get; set; } = "1";
        public bool IsEnabled { get; set; } = true;
        public List<DataPoint> DataPoints { get; set; } = new List<DataPoint>();
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string Description { get; set; } = string.Empty;
    }
}