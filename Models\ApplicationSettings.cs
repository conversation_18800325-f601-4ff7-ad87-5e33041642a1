using System;
using System.Collections.Generic;

namespace ProFaceDataExtractor.Models
{
    public class ApplicationSettings
    {
        public List<GatewayConfiguration> Gateways { get; set; } = new List<GatewayConfiguration>();
        public int ReadingInterval { get; set; } = 1000; // milliseconds
        public bool AutoExportEnabled { get; set; } = true;
        public int AutoExportInterval { get; set; } = 60000; // milliseconds
        public string ExportPath { get; set; } = "./Exports";
        public string LogLevel { get; set; } = "Information";
        public bool CompressOldFiles { get; set; } = true;
        public int FileRetentionDays { get; set; } = 30;
        public DateTime LastModified { get; set; } = DateTime.Now;
    }
}