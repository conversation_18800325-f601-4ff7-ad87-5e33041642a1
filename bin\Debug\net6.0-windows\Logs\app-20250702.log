2025-07-02 16:30:47.203 +02:00 [INF] === Iniciando aplicación ProFace Data Extractor v1.0 === {"MachineName":"HP-26DNM5365838","EnvironmentUserName":"HP-26DNM5365838\\jorge","ProcessId":36188,"ThreadId":1}
2025-07-02 16:30:47.330 +02:00 [INF] Máquina: HP-26DNM5365838, Usuario: jorge {"EnvironmentUserName":"HP-26DNM5365838\\jorge","ProcessId":36188,"ThreadId":1}
2025-07-02 16:33:09.944 +02:00 [FTL] Error fatal al iniciar la aplicación {"MachineName":"HP-26DNM5365838","EnvironmentUserName":"HP-26DNM5365838\\jorge","ProcessId":36188,"ThreadId":1}
System.IO.FileNotFoundException: Could not load file or assembly 'System.IO.Ports, Version=0.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51'. El sistema no puede encontrar el archivo especificado.
File name: 'System.IO.Ports, Version=0.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51'
   at ProFaceDataExtractor.Services.ModbusCommunicationService..ctor(ILogger logger)
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Span`1& arguments, Signature sig, Boolean constructor, Boolean wrapExceptions)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(Type serviceType)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at ProFaceDataExtractor.Forms.MainForm..ctor(IServiceProvider serviceProvider) in C:\Users\<USER>\Downloads\ProFaceDataExtractor\Forms\MainForm.cs:line 32
   at ProFaceDataExtractor.Program.Main() in C:\Users\<USER>\Downloads\ProFaceDataExtractor\Program.cs:line 50
