using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using ProFaceDataExtractor.Models.Database;
using Serilog;

namespace ProFaceDataExtractor.Services
{
    public class DatabaseService
    {
        private readonly ILogger _logger;
        private readonly string _connectionString;

        public DatabaseService(ILogger logger, string connectionString)
        {
            _logger = logger;
            _connectionString = connectionString;
        }

        /// <summary>
        /// Verifica la conexión a la base de datos
        /// </summary>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                _logger.Information("Conexión a base de datos exitosa");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error conectando a la base de datos: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Crea las tablas necesarias si no existen
        /// </summary>
        public async Task<bool> InitializeDatabaseAsync()
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var createTablesScript = GetCreateTablesScript();
                
                using var command = new SqlCommand(createTablesScript, connection);
                await command.ExecuteNonQueryAsync();

                _logger.Information("Base de datos inicializada correctamente");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error inicializando base de datos: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Inserta datos históricos en lote
        /// </summary>
        public async Task<bool> InsertDataHistoryBatchAsync(List<DataHistoryEntity> dataHistory)
        {
            if (dataHistory == null || dataHistory.Count == 0)
                return true;

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                using var transaction = connection.BeginTransaction();
                
                try
                {
                    var insertQuery = @"
                        INSERT INTO DataHistory 
                        (DataPointId, PLCId, GatewayId, DataPointName, PLCName, GatewayName, 
                         Value, DataType, NumericValue, BooleanValue, Unit, Timestamp, ReadTime, IsValid, ErrorMessage)
                        VALUES 
                        (@DataPointId, @PLCId, @GatewayId, @DataPointName, @PLCName, @GatewayName,
                         @Value, @DataType, @NumericValue, @BooleanValue, @Unit, @Timestamp, @ReadTime, @IsValid, @ErrorMessage)";

                    foreach (var data in dataHistory)
                    {
                        using var command = new SqlCommand(insertQuery, connection, transaction);
                        
                        command.Parameters.AddWithValue("@DataPointId", data.DataPointId);
                        command.Parameters.AddWithValue("@PLCId", data.PLCId);
                        command.Parameters.AddWithValue("@GatewayId", data.GatewayId);
                        command.Parameters.AddWithValue("@DataPointName", data.DataPointName);
                        command.Parameters.AddWithValue("@PLCName", data.PLCName);
                        command.Parameters.AddWithValue("@GatewayName", data.GatewayName);
                        command.Parameters.AddWithValue("@Value", data.Value);
                        command.Parameters.AddWithValue("@DataType", data.DataType);
                        command.Parameters.AddWithValue("@NumericValue", (object?)data.NumericValue ?? DBNull.Value);
                        command.Parameters.AddWithValue("@BooleanValue", (object?)data.BooleanValue ?? DBNull.Value);
                        command.Parameters.AddWithValue("@Unit", data.Unit);
                        command.Parameters.AddWithValue("@Timestamp", data.Timestamp);
                        command.Parameters.AddWithValue("@ReadTime", data.ReadTime);
                        command.Parameters.AddWithValue("@IsValid", data.IsValid);
                        command.Parameters.AddWithValue("@ErrorMessage", data.ErrorMessage);

                        await command.ExecuteNonQueryAsync();
                    }

                    transaction.Commit();
                    _logger.Information("Insertados {Count} registros de datos históricos", dataHistory.Count);
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error insertando datos históricos: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Obtiene datos históricos con filtros
        /// </summary>
        public async Task<List<DataHistoryEntity>> GetDataHistoryAsync(
            DateTime? startDate = null, 
            DateTime? endDate = null, 
            string? gatewayId = null, 
            string? plcId = null,
            int maxRecords = 1000)
        {
            var results = new List<DataHistoryEntity>();

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT TOP (@MaxRecords) 
                        Id, DataPointId, PLCId, GatewayId, DataPointName, PLCName, GatewayName,
                        Value, DataType, NumericValue, BooleanValue, Unit, Timestamp, ReadTime, IsValid, ErrorMessage
                    FROM DataHistory 
                    WHERE 1=1";

                var parameters = new List<SqlParameter>
                {
                    new("@MaxRecords", maxRecords)
                };

                if (startDate.HasValue)
                {
                    query += " AND ReadTime >= @StartDate";
                    parameters.Add(new SqlParameter("@StartDate", startDate.Value));
                }

                if (endDate.HasValue)
                {
                    query += " AND ReadTime <= @EndDate";
                    parameters.Add(new SqlParameter("@EndDate", endDate.Value));
                }

                if (!string.IsNullOrEmpty(gatewayId))
                {
                    query += " AND GatewayId = @GatewayId";
                    parameters.Add(new SqlParameter("@GatewayId", gatewayId));
                }

                if (!string.IsNullOrEmpty(plcId))
                {
                    query += " AND PLCId = @PLCId";
                    parameters.Add(new SqlParameter("@PLCId", plcId));
                }

                query += " ORDER BY ReadTime DESC";

                using var command = new SqlCommand(query, connection);
                command.Parameters.AddRange(parameters.ToArray());

                using var reader = await command.ExecuteReaderAsync();
                
                while (await reader.ReadAsync())
                {
                    results.Add(new DataHistoryEntity
                    {
                        Id = reader.GetInt64("Id"),
                        DataPointId = reader.GetString("DataPointId"),
                        PLCId = reader.GetString("PLCId"),
                        GatewayId = reader.GetString("GatewayId"),
                        DataPointName = reader.GetString("DataPointName"),
                        PLCName = reader.GetString("PLCName"),
                        GatewayName = reader.GetString("GatewayName"),
                        Value = reader.GetString("Value"),
                        DataType = reader.GetString("DataType"),
                        NumericValue = reader.IsDBNull("NumericValue") ? null : reader.GetDouble("NumericValue"),
                        BooleanValue = reader.IsDBNull("BooleanValue") ? null : reader.GetBoolean("BooleanValue"),
                        Unit = reader.GetString("Unit"),
                        Timestamp = reader.GetDateTime("Timestamp"),
                        ReadTime = reader.GetDateTime("ReadTime"),
                        IsValid = reader.GetBoolean("IsValid"),
                        ErrorMessage = reader.GetString("ErrorMessage")
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error obteniendo datos históricos: {Message}", ex.Message);
            }

            return results;
        }

        /// <summary>
        /// Inserta un log de evento
        /// </summary>
        public async Task<bool> InsertEventLogAsync(EventLogEntity eventLog)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var insertQuery = @"
                    INSERT INTO EventLogs 
                    (Level, Message, Exception, Source, Category, Timestamp, MachineName, UserName)
                    VALUES 
                    (@Level, @Message, @Exception, @Source, @Category, @Timestamp, @MachineName, @UserName)";

                using var command = new SqlCommand(insertQuery, connection);
                
                command.Parameters.AddWithValue("@Level", eventLog.Level);
                command.Parameters.AddWithValue("@Message", eventLog.Message);
                command.Parameters.AddWithValue("@Exception", eventLog.Exception);
                command.Parameters.AddWithValue("@Source", eventLog.Source);
                command.Parameters.AddWithValue("@Category", eventLog.Category);
                command.Parameters.AddWithValue("@Timestamp", eventLog.Timestamp);
                command.Parameters.AddWithValue("@MachineName", eventLog.MachineName);
                command.Parameters.AddWithValue("@UserName", eventLog.UserName);

                await command.ExecuteNonQueryAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error insertando log de evento: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Limpia datos históricos antiguos
        /// </summary>
        public async Task<int> CleanupOldDataAsync(int retentionDays)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                
                var deleteQuery = "DELETE FROM DataHistory WHERE ReadTime < @CutoffDate";
                
                using var command = new SqlCommand(deleteQuery, connection);
                command.Parameters.AddWithValue("@CutoffDate", cutoffDate);

                var deletedRows = await command.ExecuteNonQueryAsync();
                
                _logger.Information("Eliminados {Count} registros antiguos de datos históricos", deletedRows);
                return deletedRows;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error limpiando datos antiguos: {Message}", ex.Message);
                return 0;
            }
        }

        private string GetCreateTablesScript()
        {
            return @"
                -- Crear tabla Gateways si no existe
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Gateways' AND xtype='U')
                CREATE TABLE Gateways (
                    Id NVARCHAR(50) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    IpAddress NVARCHAR(15) NOT NULL,
                    Port INT NOT NULL,
                    Timeout INT NOT NULL,
                    RetryCount INT NOT NULL,
                    IsEnabled BIT NOT NULL,
                    Description NVARCHAR(500),
                    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
                    LastModified DATETIME2 NOT NULL DEFAULT GETDATE()
                );

                -- Crear tabla PLCs si no existe
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PLCs' AND xtype='U')
                CREATE TABLE PLCs (
                    Id NVARCHAR(50) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    GatewayId NVARCHAR(50) NOT NULL,
                    Brand NVARCHAR(50),
                    Model NVARCHAR(100),
                    SlaveId NVARCHAR(10) NOT NULL,
                    IsEnabled BIT NOT NULL,
                    Description NVARCHAR(500),
                    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
                    LastModified DATETIME2 NOT NULL DEFAULT GETDATE(),
                    FOREIGN KEY (GatewayId) REFERENCES Gateways(Id)
                );

                -- Crear tabla DataPoints si no existe
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DataPoints' AND xtype='U')
                CREATE TABLE DataPoints (
                    Id NVARCHAR(50) PRIMARY KEY,
                    Name NVARCHAR(100) NOT NULL,
                    PLCId NVARCHAR(50) NOT NULL,
                    Address NVARCHAR(20) NOT NULL,
                    DataType NVARCHAR(20) NOT NULL,
                    IsEnabled BIT NOT NULL,
                    ScaleFactor FLOAT NOT NULL DEFAULT 1.0,
                    Offset FLOAT NOT NULL DEFAULT 0.0,
                    Unit NVARCHAR(20),
                    Description NVARCHAR(500),
                    CreatedAt DATETIME2 NOT NULL DEFAULT GETDATE(),
                    LastModified DATETIME2 NOT NULL DEFAULT GETDATE(),
                    FOREIGN KEY (PLCId) REFERENCES PLCs(Id)
                );

                -- Crear tabla DataHistory si no existe
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DataHistory' AND xtype='U')
                CREATE TABLE DataHistory (
                    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
                    DataPointId NVARCHAR(50) NOT NULL,
                    PLCId NVARCHAR(50) NOT NULL,
                    GatewayId NVARCHAR(50) NOT NULL,
                    DataPointName NVARCHAR(100),
                    PLCName NVARCHAR(100),
                    GatewayName NVARCHAR(100),
                    Value NVARCHAR(MAX),
                    DataType NVARCHAR(20),
                    NumericValue FLOAT NULL,
                    BooleanValue BIT NULL,
                    Unit NVARCHAR(20),
                    Timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
                    ReadTime DATETIME2 NOT NULL DEFAULT GETDATE(),
                    IsValid BIT NOT NULL DEFAULT 1,
                    ErrorMessage NVARCHAR(500),
                    FOREIGN KEY (DataPointId) REFERENCES DataPoints(Id),
                    FOREIGN KEY (PLCId) REFERENCES PLCs(Id),
                    FOREIGN KEY (GatewayId) REFERENCES Gateways(Id)
                );

                -- Crear índices para DataHistory
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_DataHistory_ReadTime')
                CREATE INDEX IX_DataHistory_ReadTime ON DataHistory(ReadTime);

                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_DataHistory_GatewayId')
                CREATE INDEX IX_DataHistory_GatewayId ON DataHistory(GatewayId);

                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_DataHistory_PLCId')
                CREATE INDEX IX_DataHistory_PLCId ON DataHistory(PLCId);

                -- Crear tabla ApplicationConfig si no existe
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ApplicationConfig' AND xtype='U')
                CREATE TABLE ApplicationConfig (
                    [Key] NVARCHAR(100) PRIMARY KEY,
                    Value NVARCHAR(MAX),
                    Description NVARCHAR(500),
                    LastModified DATETIME2 NOT NULL DEFAULT GETDATE()
                );

                -- Crear tabla EventLogs si no existe
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EventLogs' AND xtype='U')
                CREATE TABLE EventLogs (
                    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
                    Level NVARCHAR(20) NOT NULL,
                    Message NVARCHAR(MAX) NOT NULL,
                    Exception NVARCHAR(MAX),
                    Source NVARCHAR(100),
                    Category NVARCHAR(100),
                    Timestamp DATETIME2 NOT NULL DEFAULT GETDATE(),
                    MachineName NVARCHAR(50),
                    UserName NVARCHAR(50)
                );

                -- Crear índice para EventLogs
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='IX_EventLogs_Timestamp')
                CREATE INDEX IX_EventLogs_Timestamp ON EventLogs(Timestamp);
            ";
        }
    }
}
