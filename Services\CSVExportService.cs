using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ProFaceDataExtractor.Models;
using Serilog;

namespace ProFaceDataExtractor.Services
{
    public class CSVExportService
    {
        private readonly ILogger _logger;
        private string _exportPath;

        public CSVExportService(ILogger logger)
        {
            _logger = logger;
            _exportPath = "./Exports";
        }

        public void SetExportPath(string path)
        {
            _exportPath = path;
            Directory.CreateDirectory(_exportPath);
        }

        public async Task ExportDataAsync(Dictionary<string, Dictionary<string, Dictionary<string, object>>> data, string filename = "")
        {
            try
            {
                if (string.IsNullOrEmpty(filename))
                {
                    filename = $"ProFaceData_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                }

                var filePath = Path.Combine(_exportPath, filename);
                var csvContent = GenerateCSVContent(data);

                await File.WriteAllTextAsync(filePath, csvContent, Encoding.UTF8);
                _logger.Information($"Datos exportados exitosamente a: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error exportando datos a CSV: {ex.Message}");
            }
        }

        public async Task AppendDataAsync(Dictionary<string, Dictionary<string, Dictionary<string, object>>> data, string filename)
        {
            try
            {
                var filePath = Path.Combine(_exportPath, filename);
                var csvRow = GenerateCSVRow(data);

                // Si el archivo no existe, crear con headers
                if (!File.Exists(filePath))
                {
                    var headers = GenerateCSVHeaders(data);
                    await File.WriteAllTextAsync(filePath, headers + Environment.NewLine, Encoding.UTF8);
                }

                await File.AppendAllTextAsync(filePath, csvRow + Environment.NewLine, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error agregando datos al CSV: {ex.Message}");
            }
        }

        private string GenerateCSVContent(Dictionary<string, Dictionary<string, Dictionary<string, object>>> data)
        {
            var sb = new StringBuilder();

            // Headers
            sb.AppendLine(GenerateCSVHeaders(data));

            // Data row
            sb.AppendLine(GenerateCSVRow(data));

            return sb.ToString();
        }

        private string GenerateCSVHeaders(Dictionary<string, Dictionary<string, Dictionary<string, object>>> data)
        {
            var headers = new List<string> { "Timestamp" };

            foreach (var gateway in data)
            {
                foreach (var plc in gateway.Value)
                {
                    foreach (var dataPoint in plc.Value)
                    {
                        headers.Add($"{gateway.Key}_{plc.Key}_{dataPoint.Key}");
                    }
                }
            }

            return string.Join(",", headers.Select(h => $"\"{h}\""));
        }

        private string GenerateCSVRow(Dictionary<string, Dictionary<string, Dictionary<string, object>>> data)
        {
            var values = new List<string> { $"\"{DateTime.Now:yyyy-MM-dd HH:mm:ss}\"" };

            foreach (var gateway in data)
            {
                foreach (var plc in gateway.Value)
                {
                    foreach (var dataPoint in plc.Value)
                    {
                        values.Add($"\"{dataPoint.Value}\"");
                    }
                }
            }

            return string.Join(",", values);
        }

        public async Task<List<string>> GetExportedFilesAsync()
        {
            try
            {
                if (!Directory.Exists(_exportPath))
                    return new List<string>();

                var files = Directory.GetFiles(_exportPath, "*.csv")
                    .Select(Path.GetFileName)
                    .Where(f => f != null)
                    .Cast<string>()
                    .OrderByDescending(f => f)
                    .ToList();

                return files;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error obteniendo archivos exportados: {ex.Message}");
                return new List<string>();
            }
        }

        public async Task CleanupOldFilesAsync(int retentionDays)
        {
            try
            {
                if (!Directory.Exists(_exportPath))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-retentionDays);
                var files = Directory.GetFiles(_exportPath, "*.csv");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        _logger.Information($"Archivo antiguo eliminado: {file}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Error limpiando archivos antiguos: {ex.Message}");
            }
        }
    }
}